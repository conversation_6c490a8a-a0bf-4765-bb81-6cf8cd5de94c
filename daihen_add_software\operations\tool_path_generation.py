# (C) 2024 ModuleWorks GmbH
# Owner: Industry Project

import math
import time
from dataclasses import dataclass
from dataclasses import field

import moduleworks as mw
from glm import mat4
from tcframework import error
from tcframework import geometry
from tcframework import operation
from tcframework import params
from tcframework.error import UserError
from tcframework.geometry import MwCurveService

from daihen_add_software import settings
from daihen_add_software.settings import OUTPUT_DIR
from daihen_add_software.settings import TP_COMBINED_TEMPLATE
from daihen_add_software.settings import TP_TEMPLATE

from ..utils import set_layer_data
from ..utils.part import Part
from ..utils.part import transform_mw_mesh


class ToolPathCalculationUpdateHandler(mw.UpdateHandler):
    def __init__(
        self, context: operation.ExecutionContext, start_progress: int, end_progress: int
    ) -> None:
        super().__init__()
        self._context: operation.ExecutionContext = context
        self._start_progress = start_progress
        self._end_progress = end_progress

    def is_canceled(self) -> bool:
        return self._context.abort

    def set_progress(
        self,
        _: mw.UpdateHandler.ProgressDescription,
        overall_progress: mw.UpdateHandler.OverallProgressDescription,
    ) -> None:
        p = overall_progress.percentage

        self._context.progress = self._start_progress + p / 100.0 * (
            self._end_progress - self._start_progress
        )

    def visual_update(self, _last_calculated: mw.cadcam.CNCMove, _cut_number: int) -> None: ...


@dataclass(frozen=True)
class ToolPathOperationInputs:
    guide_curves: params.MwCurveArray = field(
        default_factory=lambda: params.MwCurveArray(allow_none=True)
    )
    guide_mesh: params.Object[Part] = field(default_factory=lambda: params.Object(allow_none=True))
    user_transform: params.Mat4 = field(default_factory=lambda: params.Mat4(mat4()))
    machining_surface: params.Object[Part] = field(default_factory=params.Object)
    #scanned_surface: params.Object[Part] = field(default_factory=params.Object)
    #cad_surface: params.Object[Part] = field(default_factory=params.Object)
    #### TODO containment_curves
    parts: params.List[Part] = field(
        default_factory=lambda: params.List(min_length=1, contained_type=Part)
    )
    entire_machining_area: params.Bool = field(default_factory=lambda: params.Bool(value=False))
    number_of_slices: params.Int = field(default_factory=lambda: params.Int(value=1, minimum=1))

    layer_range: params.IntRange = field(
        default_factory=lambda: params.IntRange(value=(1, 1), minimum=1, maximum=1000)
    )
    detect_layers: params.Bool = field(default_factory=lambda: params.Bool(value=False))
    layer_thickness: params.Float = field(
        default_factory=lambda: params.Float(value=2.0, minimum=1e-12)
    )
    layer_step_over: params.Float = field(
        default_factory=lambda: params.Float(value=2.0, minimum=1e-12)
    )
    alternate_direction_for_each_layer: params.Bool = field(
        default_factory=lambda: params.Bool(value=False)
    )
    offset_to_curve: params.Float = field(
        default_factory=lambda: params.Float(value=0, minimum=-100)
    )
    min_angle_definition: params.Float = field(
        default_factory=lambda: params.Float(value=0, minimum=0)
    )
    max_angle_definition: params.Float = field(
        default_factory=lambda: params.Float(value=90, minimum=0)
    )
    basic_tilt_angle: params.Float = field(
        default_factory=lambda: params.Float(value=0, minimum=-270)
    )
    start_point_rotation: params.Bool = field(default_factory=lambda: params.Bool(value=False))
    pattern: params.Selection = field(
        default_factory=lambda: params.Selection(
            value="ZigZag", options=["ZigZag", "OneWay", "Spiral"]
        )
    )
    guide: params.Selection = field(
        default_factory=lambda: params.Selection(
            value="Medial curve",
            options=[
                "Guide curve",
                "Guide mesh",
                "Medial curve",
            ],
        )
    )
    containment: params.Selection = field(
        default_factory=lambda: params.Selection(
            value="Containment body mesh",
            options=[
                "Containment body mesh",
                # "Containment curve",
                "Machining surface boundary",
            ],
        )
    )
    print_base: params.Selection = field(
        default_factory=lambda: params.Selection(
            value="Machine Base", options=["Machine Base", "Hub Surface"]
        )
    )


@dataclass(frozen=True)
class ToolPathOperationOutputs:
    tool_path: params.Toolpath = field(default_factory=params.Toolpath)
    geo_libs: params.List[mw.GeoLib] = field(
        default_factory=lambda: params.List(contained_type=mw.GeoLib)
    )


class ToolPathCalculation(operation.ABC[ToolPathOperationInputs, ToolPathOperationOutputs]):
    async def run(self, context: operation.ExecutionContext) -> None:
        full_transformation = (
            self.inputs.user_transform.val_checked
            * self.inputs.machining_surface.val_checked.mesh_alignment_transform
        )

        # full_transformation = (
        #     self.inputs.user_transform.val_checked
        #     * self.inputs.scanned_surface.val_checked.mesh_alignment_transform
        # )

        # full_transformation = (
        #     self.inputs.user_transform.val_checked
        #     * self.inputs.cad_surface.val_checked.mesh_alignment_transform
        # )

        context.progress = 0

        progress_range = math.floor(
            100.0 / (self.inputs.parts.length + 1) if self.inputs.parts.length > 1 else 100.0
        )

        tp_operations: list[mw.GeoLib] = []

        machining_surface = transform_mw_mesh(
            self.inputs.machining_surface.val_checked.mw_mesh,
            full_transformation,
        )

        # scanned_surface = transform_mw_mesh(
        #     self.inputs.scanned_surface.val_checked.mw_mesh,
        #     full_transformation,
        # )

        # cad_surface = transform_mw_mesh(
        #     self.inputs.cad_surface.val_checked.mw_mesh,
        #     full_transformation,
        # )

        check_surfaces = mw.MeshArray()
        check_surfaces.add_mesh(machining_surface)

        for idx, part in enumerate(self.inputs.parts.val_checked):
            geo_lib = mw.GeoLib(machining_surface.get_units(), 0)
            geo_lib.deserialize(TP_TEMPLATE)
            start_progress = idx * progress_range
            end_progress = start_progress + progress_range
            uh = ToolPathCalculationUpdateHandler(context, start_progress, end_progress)
            geo_lib.set_update_handler(uh)
            start_layer, end_layer = self.inputs.layer_range.val_checked
            set_layer_data(
                geo_lib,
                start_layer,
                end_layer,
                self.inputs.detect_layers.val_checked,
                self.inputs.layer_thickness.val_checked,
                self.inputs.layer_step_over.val_checked,
            )
            geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.containment_curve_offset = (
                self.inputs.offset_to_curve.val_checked
            )
            geo_lib.mach_param.tool_axis_control_params.w_ort_angle_limit_start = (
                self.inputs.min_angle_definition.val_checked
            )
            geo_lib.mach_param.tool_axis_control_params.w_ort_angle_limit_end = (
                self.inputs.max_angle_definition.val_checked
            )
            geo_lib.mach_param.tool_axis_control_params.side_tilt_angle = (
                self.inputs.basic_tilt_angle.val_checked
            )

            machining_surfaces = mw.MeshArray()
            machining_surfaces.add_mesh(machining_surface)
            geo_lib.machining_surfaces = machining_surfaces

            # scanned_surfaces = mw.MeshArray()
            # scanned_surfaces.add_mesh(scanned_surface)

            # cad_surfaces = mw.MeshArray()
            # cad_surfaces.add_mesh(cad_surface)

            containment_mesh = transform_mw_mesh(part.mw_mesh, full_transformation)

            containment_surfaces = mw.MeshArray()
            containment_surfaces.add_mesh(containment_mesh)
            check_surfaces.add_mesh(containment_mesh)
            geo_lib.containment_body_surface = containment_surfaces
            start_point_rotation = self.inputs.start_point_rotation.val_checked
            alternate_direction_for_each_layer = self.inputs.alternate_direction_for_each_layer.val_checked
            geo_lib.tool_info = mw.cadcam.SphereMill(geo_lib.units)   ## comment out if the mesh holder tool needs to be passed as a toolpath calculation tool

            match self.inputs.guide.val_checked:
                case "Guide curve":
                    if self.inputs.guide_curves.val is None:
                        raise UserError("No Guide Curve present.")
                        # TODO: IND-3345 validity check
                    geo_lib.drive_curves = MwCurveService.transform_curve_array(
                        self.inputs.guide_curves.val_checked, self.inputs.user_transform.val_checked
                    )
                    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.guide_geometry_type = (
                        mw.WeldingBasedTpCalcParams.GuideGeometryType.WP_GGT_GUIDE_CURVE
                    )
                case "Guide mesh":
                    if self.inputs.guide_mesh.val is None:
                        raise UserError("No Guide Mesh present.")
                        # TODO: IND-3345 validity check
                    drive_meshes = mw.MeshArray()
                    guide_mesh = transform_mw_mesh(
                        self.inputs.guide_mesh.val_checked.mw_mesh,
                        full_transformation,
                    )
                    drive_meshes.add_mesh(guide_mesh)
                    geo_lib.slicing_drive_mesh = drive_meshes
                    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.guide_geometry_type = (
                        mw.WeldingBasedTpCalcParams.GuideGeometryType.WP_GGT_GUIDE_MESH
                    )
                case "Medial curve":
                    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.guide_geometry_type = (
                        mw.WeldingBasedTpCalcParams.GuideGeometryType.WP_GGT_MEDIAL_CURVE
                    )

            match self.inputs.containment.val_checked:
                case "Containment body mesh":
                    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.containment_type = (
                        mw.WeldingBasedTpCalcParams.ContainmentType.WP_3D_CONTAINMENT
                    )
                #### TODO case "Containment curve":
                # geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.containment_type = (
                #     mw.WeldingBasedTpCalcParams.ContainmentType.WP_2D_CONTAINMENT
                # )
                case "Machining surface boundary":
                    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.containment_type = (
                        mw.WeldingBasedTpCalcParams.ContainmentType.WP_MACHINING_SURFACE_BOUNDARY
                    )

            if self.inputs.entire_machining_area.val_checked:
                geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.containment_area_type = (
                    mw.WeldingBasedTpCalcParams.ContainmentAreaType.full
                )
            else:
                geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.containment_area_type = (
                    mw.WeldingBasedTpCalcParams.ContainmentAreaType.numberOfSlices
                )
                geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.number_of_slices = (
                    self.inputs.number_of_slices.val_checked
                )

            if self.inputs.alternate_direction_for_each_layer.val_checked:
                geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.alternate_direction_for_each_layer_flg = (
                    alternate_direction_for_each_layer
                )
            if self.inputs.pattern.val_checked == "ZigZag":
                geo_lib.mach_param.cur_mach_type = mw.FiveAxParams.MACHTYPE_ZIGZAG
            elif self.inputs.pattern.val_checked == "OneWay":
                geo_lib.mach_param.cur_mach_type = mw.FiveAxParams.MACHTYPE_ONEWAY
            else:
                geo_lib.mach_param.cur_mach_type = mw.FiveAxParams.MACHTYPE_SPIRAL

            if self.inputs.print_base.val_checked == "Machine Base":
                geo_lib.mach_param.link_params.clearance_type = mw.LinkParams.CLEARANCE_PLANE
            #else:
                #geo_lib.mach_param.link_params.clearance_type = mw.LinkParams.CLEARANCE_CYLINDER

            if self.inputs.start_point_rotation.val_checked:
                geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.automatic_start_point_rotation_flg = (
                    start_point_rotation
                )

            tp_operations.append(geo_lib)

        if settings.BIN_FILE_DUMP:
            output_directory_bins = OUTPUT_DIR / f"bins_{time.strftime('%Y%m%d-%H%M%S')}"
            output_directory_bins.mkdir(parents=True, exist_ok=True)

        output_geo_libs: list[mw.GeoLib] = []
        for ind, geo_lib in enumerate(tp_operations):
            try:
                geo_lib.calc_tool_path(mw.GeoLib.CollisionsReport())
            except mw.misc.MwException as mwE:
                raise error.UserError(str(mwE))
            if settings.BIN_FILE_DUMP:
                file_path = output_directory_bins / f"toolpath_{ind}.bin"
                geo_lib.serialize(file_path, geo_lib.SerializationSettings())
            output_geo_libs.append(geo_lib)

        if len(tp_operations) > 1:
            geo_lib_combined = mw.GeoLib(machining_surface.get_units(), 0)
            geo_lib_combined.deserialize(TP_COMBINED_TEMPLATE)
            geo_lib_combined.check_surf_array1 = check_surfaces
            geo_lib_combined.original_tool_path_vec = [g.tool_path for g in tp_operations]
            uh = ToolPathCalculationUpdateHandler(context, len(tp_operations) * progress_range, 100)
            geo_lib_combined.set_update_handler(uh)
            try:
                geo_lib_combined.calc_tool_path(mw.GeoLib.CollisionsReport())
            except mw.misc.MwException as mwE:
                raise error.UserError(str(mwE))
            output_geo_libs.append(geo_lib_combined)

            if settings.BIN_FILE_DUMP:
                file_path = output_directory_bins / f"combined_toolpath.bin"
                geo_lib_combined.serialize(file_path, geo_lib_combined.SerializationSettings())

        # Set outputs not before the end of the operation, to prevent invalid state in case the operation is aborted mid calculation:
        if len(output_geo_libs) >= 1:
            self.outputs.tool_path.val = geometry.Toolpath(output_geo_libs[-1].tool_path)
        self.outputs.geo_libs.val = output_geo_libs

        context.progress = 100
