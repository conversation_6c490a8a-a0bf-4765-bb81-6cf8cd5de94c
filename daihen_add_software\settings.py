# (C) 2024 ModuleWorks GmbH

from pathlib import Path

from tcframework.utility import get_free_port_in_range

OUTPUT_DIR = Path(__file__).parent.parent / "output"

ASSETS = Path(__file__).parent.parent / "assets"

#MXP_DIR = ASSETS / "mxps"

TP_TEMPLATE = ASSETS / "reference_files" / "tp_template.json"
TP_COMBINED_TEMPLATE = ASSETS / "reference_files" / "tp_combined_template.json"
MACHINE_BASE_STL = ASSETS / "geometry_data" / "machining_surface.stl"
#SCANNED_BASE_STL = ASSETS / "geometry_data" / "Scanned_Mesh.stl"
#CAD_BASE_STL = ASSETS / "geometry_data" / "CAD_Base.stl"

# Start port at 50000 to avoid sharing ports with SimulationHandler
POSTING_PORT = get_free_port_in_range(start_port=51000)
POST_PROCESSORS = Path(__file__).parent.parent / "postprocessors"
MACHINE_XML = (
    POST_PROCESSORS / "robot_models" / "FD-A20_in_Rokko" / "FD-A20_in_Rokko.xml"
)
STATIC_UI = ASSETS / "static"

APP_ICON = STATIC_UI / "daihen_icon.ico"
BANNER = STATIC_UI / "daihen_logo.png"

MAIN_COLOR_HEX = "#0971B7"
MAIN_COLOR_RGB = (9.0 / 255.0, 113.0 / 255.0, 183.0 / 255.0)
SECOND_COLOR_HEX = "#0971B7"
SECOND_COLOR_RGB = (9.0 / 255.0, 113.0 / 255.0, 183.0 / 255.0)

# Dump bin-files into output folder
BIN_FILE_DUMP = True
