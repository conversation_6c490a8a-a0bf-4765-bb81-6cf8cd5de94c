from __future__ import annotations

from tcframework import box_ui, params, ui
import json
from pathlib import Path

MATERIALS_FILE = Path(__file__).parent.parent.parent / "assets" / "reference_files" / "materials.json"

def load_materials():
    with open(MATERIALS_FILE, "r") as f:
        data = json.load(f)
    return data["materials"]

MATERIALS = load_materials()
MATERIAL_OPTIONS = [m["name"] for m in MATERIALS]
MATERIAL_TO_PROPS = {m["name"]: m for m in MATERIALS}

# Shared state for sync between tabs
if not hasattr(ui, "_welding_sync_state"):
    # Initialize with first material's values
    first_material = MATERIAL_OPTIONS[0]
    first_props = MATERIAL_TO_PROPS[first_material]

    ui._welding_sync_state = {
        "material": params.Selection(value=first_material, options=MATERIAL_OPTIONS),
        "welding_params": {},
        "version": params.Int(value=0),
        # Single set of parameter objects that will be updated when material changes
        "current_welding_current": params.Int(value=first_props["welding_parameters"]["current_set"]),
        "current_welding_voltage": params.Int(value=first_props["welding_parameters"]["voltage_set"]),
        "current_welding_speed": params.Int(value=first_props["welding_parameters"]["travel_speed"]),
    }
state = ui._welding_sync_state

# Initialize welding params for all materials (use params, not tcparams)
for mat in MATERIAL_OPTIONS:
    if mat not in state["welding_params"]:
        props = MATERIAL_TO_PROPS[mat]
        state["welding_params"][mat] = {
            "welding_current": params.Int(value=props["welding_parameters"]["current_set"]),
            "welding_voltage": params.Int(value=props["welding_parameters"]["voltage_set"]),
            "welding_speed": params.Int(value=props["welding_parameters"]["travel_speed"]),
        }

def get_ui(b: ui.Builder) -> list[ui.UiNode]:
    material_selection = state["material"]

    # Get the single set of parameter objects that will be updated
    welding_current = state["current_welding_current"]
    welding_voltage = state["current_welding_voltage"]
    welding_speed = state["current_welding_speed"]

    def update_welding_params_on_material_change(selected_material):
        props = MATERIAL_TO_PROPS.get(selected_material)
        if props:
            # Update the single set of parameter objects with new material values
            welding_current.value = props["welding_parameters"]["current_set"]
            welding_voltage.value = props["welding_parameters"]["voltage_set"]
            welding_speed.value = props["welding_parameters"]["travel_speed"]

            # Also update the stored values for this material
            if selected_material not in state["welding_params"]:
                state["welding_params"][selected_material] = {
                    "welding_current": params.Int(value=props["welding_parameters"]["current_set"]),
                    "welding_voltage": params.Int(value=props["welding_parameters"]["voltage_set"]),
                    "welding_speed": params.Int(value=props["welding_parameters"]["travel_speed"]),
                }
            else:
                state["welding_params"][selected_material]["welding_current"].value = props["welding_parameters"]["current_set"]
                state["welding_params"][selected_material]["welding_voltage"].value = props["welding_parameters"]["voltage_set"]
                state["welding_params"][selected_material]["welding_speed"].value = props["welding_parameters"]["travel_speed"]

    def save_current_params_to_material():
        """Save current parameter values back to the selected material's storage"""
        current_material = material_selection.value
        if current_material in state["welding_params"]:
            state["welding_params"][current_material]["welding_current"].value = welding_current.value
            state["welding_params"][current_material]["welding_voltage"].value = welding_voltage.value
            state["welding_params"][current_material]["welding_speed"].value = welding_speed.value

    # Set up change handlers to save values back to material storage
    welding_current.on_change(lambda v: save_current_params_to_material())
    welding_voltage.on_change(lambda v: save_current_params_to_material())
    welding_speed.on_change(lambda v: save_current_params_to_material())

    material_selection.on_change(lambda v: update_welding_params_on_material_change(v))

    return [
        box_ui.number_field(b, welding_current, text="Current Set (A)"),
        box_ui.number_field(b, welding_voltage, text="Voltage Set (V)"),
        box_ui.number_field(b, welding_speed, text="Travel Speed (mm/s)"),
    ]