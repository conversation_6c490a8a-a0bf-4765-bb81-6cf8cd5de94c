from tcframework import storage

from daihen_add_software.ui_blocks.calculation_pipe import CalculationStorage
from daihen_add_software.ui_blocks.parameter_setup_pipe import ParameterStorage
from daihen_add_software.ui_blocks.part_setup_pipe import PartStorage


class Storage(storage.Node):
    part_storage: PartStorage = PartStorage()
    parameter_storage: ParameterStorage = ParameterStorage()
    calculation_storage: CalculationStorage = CalculationStorage()
    operation: storage.OperationNode = storage.OperationNode()
