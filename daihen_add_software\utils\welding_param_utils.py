# (C) 2024 ModuleWorks GmbH

import moduleworks as mw


def set_layer_data(
    geo_lib: mw.GeoLib,
    start_layer: int,
    end_layer: int,
    detect_layers: bool,
    layer_thickness: float,
    step_over: float,
) -> None:
    # Set end_layer to 1 for detect_remaining_layers, so that all layers get the same layer_infos. For now not necessary, because all layers have the same layer_infos anyways, but might change in the future.
    if detect_layers:
        start_layer = 1
        end_layer = 1

    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.start_layer = (
        start_layer
    )
    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.end_layer = (
        end_layer
    )
    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.detect_number_of_layers_flg = (
        detect_layers
    )
    layer_info = mw.WeldingBasedTpCalcParams.LayerInfos()

    for _ in range(end_layer):
        layer_data = mw.WeldingBasedTpCalcParams.LayerData(geo_lib.units)
        layer_data.thickness = layer_thickness
        layer_data.stepover = step_over
        layer_info.append(layer_data)

    gaps_along = geo_lib.mach_param.link_params.gaps_along_cut
    gaps_along.gap_size.value = 2 * step_over
    link_between = geo_lib.mach_param.link_params.link_between_slices
    link_between.gap_size.value = 2 * step_over

    geo_lib.mach_param.tp_calculation_methods_params.welding_based_tp_calc_params.layer_infos = (
        layer_info
    )


def extract_drive_mesh_from_containment(
    machining_surface: mw.cadcam.TMesh,
    containment: mw.cadcam.TMesh,
    tolerance: float = 1e-8,
) -> mw.cadcam.TMesh:
    """Remove elements on machining surfaces from containment

    For hollow parts, we need a drive mesh that is aligned with the containment mesh except for the
    faces on the machining surface. Therefore, this method creates a new mesh that is equivalent to
    the containment, but without the triangles that are placed on the machining surface.

    Args:
        machining_surface (mw.cadcam.TMesh): The machining surfaces
        containment (mw.cadcam.TMesh): The containment meshes
        tolerance (float): The tolerance for distance check. Defaults to 1e-8.

    Returns:
        mw.cadcam.TMesh: The drive meshes
    """
    ttvec: mw.cadcam.TTriangleVector = mw.cadcam.TTriangleVector()

    triangles: mw.cadcam.ContainerMesh.TriangleArray = containment.triangles
    points: mw.cadcam.PointArray = containment.points
    point_zero = mw.cadcam.TPoint3d(0, 0, 0)
    dist_info = mw.DistToolMeshPointInf(machining_surface, 1, 1, True)

    triangle: mw.cadcam.TTriangle
    for triangle in triangles:
        first_point = points[triangle.first_point_index]
        second_point = points[triangle.second_point_index]
        third_point = points[triangle.third_point_index]

        if (
            dist_info.get_distance(first_point, point_zero, point_zero) <= tolerance
            and dist_info.get_distance(second_point, point_zero, point_zero) <= tolerance
            and dist_info.get_distance(third_point, point_zero, point_zero) <= tolerance
        ):
            continue

        ttvec.add_triangle(first_point, second_point, third_point, triangle.normal)
    drive_mesh = mw.cadcam.TMesh(containment.get_units())
    drive_mesh.set_triangles(ttvec)
    return drive_mesh
