import moduleworks as mw
from glm import vec3
from moduleworks import cncsim
from tcframework import box_ui
from tcframework import geometry
from tcframework import params
from tcframework import ui
from tcframework import utility
from tcframework.utility import simulation_handler

from daihen_add_software import settings

from . import calculation_pipe
from . import part_setup_pipe


def create_containment_meshes(
    part_params: part_setup_pipe.Params, part_meshes: params.List[params.Mesh]
):
    if part_params.containment_meshes_active.value and part_params.containment_meshes.value:
        result: list[params.Mesh] = []
        for part in part_params.containment_meshes.val_checked:
            part_mesh = params.Mesh(part.display_mesh)
            result.append(part_mesh)
        part_meshes.val = result


def get_ui(
    b: ui.Builder,
    part_params: part_setup_pipe.Params,
    calculation_params: calculation_pipe.Params,
) -> tuple[list[ui.UiNode], simulation_handler.SimulationHandler]:
    simulation = simulation_handler.SimulationHandler()
    part_meshes: params.List[params.Mesh] = params.List(min_length=1, contained_type=params.Mesh)
    guide_mesh: params.Mesh = params.Mesh()
    machsurf_mesh: params.Mesh = params.Mesh()
    # containment_curve_pl = params.Polyline()

    part_params.guide_mesh.map(guide_mesh, lambda gm: gm.display_mesh)
    part_params.machining_surface.map(machsurf_mesh, lambda ms: ms.display_mesh)

    def get_workpiece_transform(machine_xml) -> mw.cadcam.Matrix4_float:
        engine = cncsim.create_engine()
        engine.initialize_from_xml(str(machine_xml))  # TODO: remove gedik specific
        workpiece_transform_node = engine.get_node_by_name("workpiece_transform")
        mw_matrix = workpiece_transform_node.global_matrix
        if workpiece_transform_node.id < 0:
            raise Exception("The Machine definition doesn't have a 'workpiece_transform' node.")
        return mw_matrix

    workpiece_transform = params.Mat4()
    workpiece_transform.mw_matrix = get_workpiece_transform(settings.MACHINE_XML)
    simulation.toolpath.transformation.map(workpiece_transform, skip_initial_evaluation=True)
    machine_not_loaded = params.Bool(True)

    def load_machine() -> None:
        simulation.load_machine_from_folder(settings.MACHINE_XML.parent)
        machine_not_loaded.val = False

    # part_params.containment_curves.map(containment_curve_pl, mapping=lambda crv: polygonize(crv))

    # Use an extra parameter so that we are able to clear the reactions for parts without interfering
    # with other reactions of the simulation.toolpath.transformation:

    def update_transformations() -> None:
        full_transformation = (
            workpiece_transform.val_checked * part_params.user_transform.val_checked
        )
        if part_meshes.val and part_params.containment_meshes.val:
            for part_mesh, containment_mesh in zip(
                part_meshes.val, part_params.containment_meshes.val
            ):
                if containment_mesh.mesh_alignment_transform:
                    part_mesh.transformation.val = (
                        full_transformation * containment_mesh.mesh_alignment_transform
                    )

        if part_params.guide_mesh.val:
            guide_mesh.transformation.val = (
                full_transformation * part_params.guide_mesh.val_checked.mesh_alignment_transform
            )
        if part_params.machining_surface.val:
            machsurf_mesh.transformation.val = (
                full_transformation
                * part_params.machining_surface.val_checked.mesh_alignment_transform
            )

    part_params.containment_meshes.on_any_change(
        lambda _: create_containment_meshes(part_params, part_meshes)
    )
    part_params.user_transform.on_any_change(lambda _: update_transformations())
    part_params.guide_mesh.on_any_change(lambda _: update_transformations())
    part_params.machining_surface.on_any_change(lambda _: update_transformations())

    part_params.containment_meshes.on_any_change(lambda _: update_transformations())
    simulation.toolpath.transformation.on_any_change(lambda _: update_transformations())

    simulation.toolpath.transformation.map(calculation_params.tool_path.transformation)

    def load_mxp(mxp_file: utility.File) -> None:
        if machine_not_loaded.val:
            load_machine()
        simulation.load_mxp_file(mxp_file.path)

    calculation_params.mxp_file.on_change(load_mxp, skip_initial_evaluation=True)

    simulation_playback = simulation_handler.SimulationPlayback(simulation.move_count)
    simulation_playback.drive_simulation(simulation)

    return (
        [
            box_ui.element_row(
                b,
                widget=ui.button(b, load_machine, label="Load Machine", enabled=machine_not_loaded),
            ),
            box_ui.cnc_sim_playback_controls(
                b,
                simulation_playback,
                toolpath_follow_range=simulation.toolpath_filtering.simulation_follow_range,
            ),
            box_ui.cnc_simulation(b, port=simulation.port),
            box_ui.toolpath(
                b,
                simulation.toolpath,
                name="Posted Toolpath",
                toolpath_filtering=simulation.toolpath_filtering,
            ),
            #  box_ui.active_if(
            #     b,
            #     part_params.containment_curves_active,
            #     box_ui.polyline(b, containment_curve_pl, name="Containment Curve"),
            # ),
            box_ui.active_if(
                b,
                part_params.containment_meshes_active,
                box_ui.dynamic_list(
                    b,
                    box_ui.mesh(b, part_meshes, name="Part", color=settings.MAIN_COLOR_RGB),
                    part_meshes,
                ),
            ),
            box_ui.mesh(b, machsurf_mesh, name="Machining Surface"),
            box_ui.active_if(
                b, part_params.guide_mesh_active, box_ui.mesh(b, guide_mesh, name="Guide Mesh")
            ),
        ],
        simulation,
    )
