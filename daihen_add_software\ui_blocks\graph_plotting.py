"""
Graph plotting functionality for machine axes vs move points visualization.
This module provides plotting capabilities that are not available in tcframework.
"""

from __future__ import annotations

import base64
import io
import json
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from tcframework import box_ui, params, ui

try:
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    import matplotlib.pyplot as plt
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.offline import plot
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class GraphPlotter:
    """Graph plotting utility for machine axes and toolpath data"""

    def __init__(self):
        self.plot_data = {}
        self.current_plot_type = "axes_vs_moves"

    def create_svg_plot(self, plot_data: Dict[str, List[float]], plot_type: str = "axes_vs_moves") -> str:
        """Create a simple SVG plot without external dependencies"""
        try:
            move_indices = plot_data.get('move_index', [])

            if not move_indices:
                return """
                <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                    <rect width="800" height="400" fill="#f9f9f9" stroke="#ccc"/>
                    <text x="400" y="200" text-anchor="middle" font-family="Arial" font-size="16">
                        No toolpath data available. Calculate toolpath first.
                    </text>
                </svg>
                """

            # SVG dimensions
            width, height = 800, 600
            margin = 60
            plot_width = width - 2 * margin
            plot_height = height - 2 * margin

            # Get data for joint angles
            joint_1 = plot_data.get('joint_1', [])
            joint_2 = plot_data.get('joint_2', [])
            joint_3 = plot_data.get('joint_3', [])

            if not joint_1:
                return """
                <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                    <rect width="800" height="400" fill="#f9f9f9" stroke="#ccc"/>
                    <text x="400" y="200" text-anchor="middle" font-family="Arial" font-size="16">
                        No joint data available in toolpath.
                    </text>
                </svg>
                """

            # Calculate scales
            max_move = max(move_indices) if move_indices else 1
            min_joint = min(min(joint_1), min(joint_2), min(joint_3))
            max_joint = max(max(joint_1), max(joint_2), max(joint_3))
            joint_range = max_joint - min_joint if max_joint != min_joint else 1

            def scale_x(move_idx):
                return margin + (move_idx / max_move) * plot_width

            def scale_y(joint_val):
                return margin + plot_height - ((joint_val - min_joint) / joint_range) * plot_height

            # Create SVG
            svg_parts = [
                f'<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">',
                f'<rect width="{width}" height="{height}" fill="white" stroke="#ccc"/>',

                # Title
                '<text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold">Machine Joint Angles vs Move Points</text>',

                # Axes
                f'<line x1="{margin}" y1="{margin}" x2="{margin}" y2="{height-margin}" stroke="black" stroke-width="2"/>',
                f'<line x1="{margin}" y1="{height-margin}" x2="{width-margin}" y2="{height-margin}" stroke="black" stroke-width="2"/>',

                # Axis labels
                '<text x="400" y="590" text-anchor="middle" font-family="Arial" font-size="12">Move Index</text>',
                '<text x="20" y="300" text-anchor="middle" font-family="Arial" font-size="12" transform="rotate(-90 20 300)">Joint Angle (degrees)</text>',

                # Grid lines
            ]

            # Add grid lines
            for i in range(5):
                y = margin + (i * plot_height / 4)
                svg_parts.append(f'<line x1="{margin}" y1="{y}" x2="{width-margin}" y2="{y}" stroke="#e0e0e0" stroke-width="1"/>')

                # Y-axis labels
                joint_val = max_joint - (i * joint_range / 4)
                svg_parts.append(f'<text x="{margin-10}" y="{y+5}" text-anchor="end" font-family="Arial" font-size="10">{joint_val:.1f}</text>')

            # Plot lines for joints
            colors = ['#ff0000', '#00ff00', '#0000ff']  # Red, Green, Blue
            joint_names = ['Joint 1', 'Joint 2', 'Joint 3']
            joint_data = [joint_1, joint_2, joint_3]

            for idx, (joints, color, name) in enumerate(zip(joint_data, colors, joint_names)):
                if len(joints) > 1:
                    path_data = f'M {scale_x(0)} {scale_y(joints[0])}'
                    for i in range(1, len(joints)):
                        path_data += f' L {scale_x(i)} {scale_y(joints[i])}'

                    svg_parts.append(f'<path d="{path_data}" stroke="{color}" stroke-width="2" fill="none"/>')

                    # Legend
                    legend_y = 60 + idx * 20
                    svg_parts.append(f'<line x1="650" y1="{legend_y}" x2="680" y2="{legend_y}" stroke="{color}" stroke-width="3"/>')
                    svg_parts.append(f'<text x="690" y="{legend_y+5}" font-family="Arial" font-size="12">{name}</text>')

            svg_parts.append('</svg>')

            return '\n'.join(svg_parts)

        except Exception as e:
            return f"""
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <rect width="800" height="400" fill="#ffe0e0" stroke="#ff0000"/>
                <text x="400" y="200" text-anchor="middle" font-family="Arial" font-size="16">
                    Error creating plot: {str(e)}
                </text>
            </svg>
            """

    def create_ascii_plot(self, plot_data: Dict[str, List[float]]) -> str:
        """Create a simple ASCII representation of the joint data"""
        try:
            move_indices = plot_data.get('move_index', [])
            if not move_indices:
                return "No data available for ASCII plot"

            joint_1 = plot_data.get('joint_1', [])
            if not joint_1:
                return "No joint data available"

            # Create a simple ASCII plot for Joint 1
            width = 60
            height = 10

            min_val = min(joint_1)
            max_val = max(joint_1)
            val_range = max_val - min_val if max_val != min_val else 1

            # Sample data points to fit width
            step = max(1, len(joint_1) // width)
            sampled_data = joint_1[::step][:width]

            plot_lines = []
            plot_lines.append(f"Joint 1 Angle Plot ({len(joint_1)} moves)")
            plot_lines.append(f"Range: {min_val:.1f}° to {max_val:.1f}°")
            plot_lines.append("=" * width)

            # Create ASCII plot
            for row in range(height):
                line = ""
                threshold = max_val - (row * val_range / height)
                for val in sampled_data:
                    if val >= threshold:
                        line += "*"
                    else:
                        line += " "
                plot_lines.append(f"{threshold:6.1f}|{line}")

            plot_lines.append(" " * 6 + "+" + "-" * width)
            plot_lines.append(f"      Move progression ({len(sampled_data)} samples)")

            return "\n".join(plot_lines)

        except Exception as e:
            return f"Error creating ASCII plot: {str(e)}"
        
    def extract_toolpath_data(self, geo_libs: List[Any]) -> Dict[str, List[float]]:
        """Extract plotting data from geo_libs (ModuleWorks GeoLib objects)"""
        plot_data = {
            'move_index': [],
            'x_positions': [],
            'y_positions': [],
            'z_positions': [],
            'joint_1': [],
            'joint_2': [],
            'joint_3': [],
            'joint_4': [],
            'joint_5': [],
            'joint_6': [],
            'feed_rates': [],
            'layer_numbers': []
        }
        
        try:
            if not geo_libs:
                return plot_data
                
            # Get the latest geo_lib
            geo_lib = geo_libs[-1] if isinstance(geo_libs, list) else geo_libs
            
            if hasattr(geo_lib, 'tool_path'):
                tool_path = geo_lib.tool_path
                move_count = 0
                
                # Extract move data
                if hasattr(tool_path, 'moves'):
                    for i, move in enumerate(tool_path.moves):
                        plot_data['move_index'].append(i)
                        
                        # Extract position data
                        if hasattr(move, 'position'):
                            pos = move.position
                            plot_data['x_positions'].append(getattr(pos, 'x', 0.0))
                            plot_data['y_positions'].append(getattr(pos, 'y', 0.0))
                            plot_data['z_positions'].append(getattr(pos, 'z', 0.0))
                        else:
                            plot_data['x_positions'].append(0.0)
                            plot_data['y_positions'].append(0.0)
                            plot_data['z_positions'].append(0.0)
                        
                        # Extract joint values
                        if hasattr(move, 'joint_values') and move.joint_values:
                            joints = move.joint_values
                            plot_data['joint_1'].append(joints[0] if len(joints) > 0 else 0.0)
                            plot_data['joint_2'].append(joints[1] if len(joints) > 1 else 0.0)
                            plot_data['joint_3'].append(joints[2] if len(joints) > 2 else 0.0)
                            plot_data['joint_4'].append(joints[3] if len(joints) > 3 else 0.0)
                            plot_data['joint_5'].append(joints[4] if len(joints) > 4 else 0.0)
                            plot_data['joint_6'].append(joints[5] if len(joints) > 5 else 0.0)
                        else:
                            # Default joint values if not available
                            for joint_key in ['joint_1', 'joint_2', 'joint_3', 'joint_4', 'joint_5', 'joint_6']:
                                plot_data[joint_key].append(0.0)
                        
                        # Extract feed rate
                        feed_rate = getattr(move, 'feed_rate', 0.0)
                        plot_data['feed_rates'].append(feed_rate)
                        
                        # Extract layer information (if available)
                        layer_num = getattr(move, 'layer_number', 1)
                        plot_data['layer_numbers'].append(layer_num)
                        
                        move_count += 1
                
                print(f"Extracted data for {move_count} moves")
                
        except Exception as e:
            print(f"Error extracting toolpath data: {e}")
            
        return plot_data
    
    def create_matplotlib_plot(self, plot_data: Dict[str, List[float]], plot_type: str = "axes_vs_moves") -> str:
        """Create matplotlib plot and return as base64 encoded image"""
        if not MATPLOTLIB_AVAILABLE:
            return ""
            
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle('Machine Axes vs Move Points Analysis', fontsize=14)
            
            move_indices = plot_data.get('move_index', [])
            
            if not move_indices:
                # Create empty plot with message
                fig.text(0.5, 0.5, 'No toolpath data available\nCalculate toolpath first', 
                        ha='center', va='center', fontsize=12)
            else:
                # Plot 1: Joint angles over moves
                axes[0, 0].plot(move_indices, plot_data.get('joint_1', []), label='Joint 1', linewidth=1)
                axes[0, 0].plot(move_indices, plot_data.get('joint_2', []), label='Joint 2', linewidth=1)
                axes[0, 0].plot(move_indices, plot_data.get('joint_3', []), label='Joint 3', linewidth=1)
                axes[0, 0].set_title('Robot Joint Angles')
                axes[0, 0].set_xlabel('Move Index')
                axes[0, 0].set_ylabel('Angle (degrees)')
                axes[0, 0].legend()
                axes[0, 0].grid(True, alpha=0.3)
                
                # Plot 2: More joint angles
                axes[0, 1].plot(move_indices, plot_data.get('joint_4', []), label='Joint 4', linewidth=1)
                axes[0, 1].plot(move_indices, plot_data.get('joint_5', []), label='Joint 5', linewidth=1)
                axes[0, 1].plot(move_indices, plot_data.get('joint_6', []), label='Joint 6', linewidth=1)
                axes[0, 1].set_title('Robot Joint Angles (4-6)')
                axes[0, 1].set_xlabel('Move Index')
                axes[0, 1].set_ylabel('Angle (degrees)')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)
                
                # Plot 3: XYZ positions
                axes[1, 0].plot(move_indices, plot_data.get('x_positions', []), label='X', linewidth=1)
                axes[1, 0].plot(move_indices, plot_data.get('y_positions', []), label='Y', linewidth=1)
                axes[1, 0].plot(move_indices, plot_data.get('z_positions', []), label='Z', linewidth=1)
                axes[1, 0].set_title('Tool Position (XYZ)')
                axes[1, 0].set_xlabel('Move Index')
                axes[1, 0].set_ylabel('Position (mm)')
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
                
                # Plot 4: Feed rates and layers
                ax4 = axes[1, 1]
                ax4_twin = ax4.twinx()
                
                line1 = ax4.plot(move_indices, plot_data.get('feed_rates', []), 'b-', label='Feed Rate', linewidth=1)
                line2 = ax4_twin.plot(move_indices, plot_data.get('layer_numbers', []), 'r-', label='Layer', linewidth=1)
                
                ax4.set_title('Feed Rate & Layer Information')
                ax4.set_xlabel('Move Index')
                ax4.set_ylabel('Feed Rate (mm/min)', color='b')
                ax4_twin.set_ylabel('Layer Number', color='r')
                
                # Combine legends
                lines = line1 + line2
                labels = [l.get_label() for l in lines]
                ax4.legend(lines, labels, loc='upper left')
                
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # Save to base64 string
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)
            
            return image_base64
            
        except Exception as e:
            print(f"Error creating matplotlib plot: {e}")
            return ""
    
    def create_plotly_plot(self, plot_data: Dict[str, List[float]], plot_type: str = "axes_vs_moves") -> str:
        """Create interactive plotly plot and return as HTML"""
        if not PLOTLY_AVAILABLE:
            return ""
            
        try:
            move_indices = plot_data.get('move_index', [])
            
            if not move_indices:
                # Create empty plot with message
                fig = go.Figure()
                fig.add_annotation(
                    text="No toolpath data available<br>Calculate toolpath first",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5, xanchor='center', yanchor='middle',
                    showarrow=False, font=dict(size=16)
                )
                fig.update_layout(title="Machine Axes vs Move Points Analysis")
            else:
                # Create subplots
                from plotly.subplots import make_subplots
                
                fig = make_subplots(
                    rows=2, cols=2,
                    subplot_titles=('Robot Joint Angles (1-3)', 'Robot Joint Angles (4-6)', 
                                  'Tool Position (XYZ)', 'Feed Rate & Layer Info'),
                    specs=[[{"secondary_y": False}, {"secondary_y": False}],
                           [{"secondary_y": False}, {"secondary_y": True}]]
                )
                
                # Joint angles 1-3
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_1', []), 
                                       name='Joint 1', line=dict(width=1)), row=1, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_2', []), 
                                       name='Joint 2', line=dict(width=1)), row=1, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_3', []), 
                                       name='Joint 3', line=dict(width=1)), row=1, col=1)
                
                # Joint angles 4-6
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_4', []), 
                                       name='Joint 4', line=dict(width=1)), row=1, col=2)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_5', []), 
                                       name='Joint 5', line=dict(width=1)), row=1, col=2)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_6', []), 
                                       name='Joint 6', line=dict(width=1)), row=1, col=2)
                
                # XYZ positions
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('x_positions', []), 
                                       name='X Position', line=dict(width=1)), row=2, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('y_positions', []), 
                                       name='Y Position', line=dict(width=1)), row=2, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('z_positions', []), 
                                       name='Z Position', line=dict(width=1)), row=2, col=1)
                
                # Feed rates and layers
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('feed_rates', []), 
                                       name='Feed Rate', line=dict(width=1, color='blue')), 
                             row=2, col=2)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('layer_numbers', []), 
                                       name='Layer', line=dict(width=1, color='red'), yaxis='y2'), 
                             row=2, col=2, secondary_y=True)
                
                # Update layout
                fig.update_layout(
                    title="Machine Axes vs Move Points Analysis",
                    height=600,
                    showlegend=True
                )
                
                # Update axes labels
                fig.update_xaxes(title_text="Move Index")
                fig.update_yaxes(title_text="Angle (degrees)", row=1, col=1)
                fig.update_yaxes(title_text="Angle (degrees)", row=1, col=2)
                fig.update_yaxes(title_text="Position (mm)", row=2, col=1)
                fig.update_yaxes(title_text="Feed Rate (mm/min)", row=2, col=2)
                fig.update_yaxes(title_text="Layer Number", row=2, col=2, secondary_y=True)
            
            # Convert to HTML
            html_str = plot(fig, output_type='div', include_plotlyjs=True)
            return html_str
            
        except Exception as e:
            print(f"Error creating plotly plot: {e}")
            return ""


def get_ui(b: ui.Builder, calculation_params, geo_libs_param=None) -> list[ui.UiNode]:
    """
    Create graph plotting UI elements

    Args:
        b: UI Builder
        calculation_params: Calculation parameters containing geo_libs
        geo_libs_param: Optional direct geo_libs parameter
    """

    plotter = GraphPlotter()

    # Create reactive parameters for plot control
    plot_type = params.Selection(
        value="axes_vs_moves",
        options=["axes_vs_moves", "joint_analysis", "position_analysis"]
    )

    refresh_trigger = params.Int(value=0)

    # Create a status message parameter with dynamic options
    initial_status = "Ready - Click 'Analyze Data' after calculating toolpath"
    status_message = params.Selection(
        value=initial_status,
        options=[initial_status, "Analyzing...", "Analysis Complete", "No Data Available", "Error"]
    )

    # Create a file parameter to save and display the SVG plot
    plot_file = params.File()

    # Create a text area to show ASCII plot representation
    plot_display = params.Selection(
        value="No plot generated yet",
        options=["No plot generated yet", "Plot generated - check download"]
    )
    
    def analyze_data():
        """Analyze the toolpath data and update status"""
        try:
            # Update to analyzing status
            status_message.value = "Analyzing..."

            # Get geo_libs from calculation params
            geo_libs = None
            if hasattr(calculation_params, 'geo_libs') and calculation_params.geo_libs.value:
                geo_libs = calculation_params.geo_libs.value
            elif geo_libs_param and geo_libs_param.value:
                geo_libs = geo_libs_param.value

            if geo_libs:
                # Extract data for analysis
                plot_data = plotter.extract_toolpath_data(geo_libs)
                move_count = len(plot_data.get('move_index', []))

                if move_count > 0:
                    # Generate SVG plot (always available)
                    svg_content = plotter.create_svg_plot(plot_data, plot_type.value)

                    # Save SVG plot to a file and create HTML wrapper for display
                    if svg_content:
                        try:
                            import tempfile
                            import os

                            # Create a temporary directory for plots
                            temp_dir = tempfile.gettempdir()
                            svg_file_path = os.path.join(temp_dir, "waam_graph_plot.svg")
                            html_file_path = os.path.join(temp_dir, "waam_graph_plot.html")

                            # Save SVG file
                            with open(svg_file_path, 'w', encoding='utf-8') as f:
                                f.write(svg_content)

                            # Create HTML wrapper for better display
                            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>WAAM Graph Analysis</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .plot-container {{ text-align: center; }}
        .info {{ background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="plot-container">
        <h2>WAAM Machine Axes Analysis</h2>
        <div class="info">
            <p><strong>Data Summary:</strong> {move_count} moves, {len(set(plot_data.get('layer_numbers', [])))} layers</p>
            <p><strong>Analysis Type:</strong> {plot_type.value}</p>
        </div>
        {svg_content}
        <div class="info">
            <p>Joint ranges and detailed analysis are available in the console output.</p>
            <p>This plot shows robot joint angles vs move progression.</p>
        </div>
    </div>
</body>
</html>
                            """

                            # Save HTML file
                            with open(html_file_path, 'w', encoding='utf-8') as f:
                                f.write(html_content)

                            # Update the file parameter to point to the HTML file
                            plot_file.value = html_file_path
                            print(f"📊 Plot files saved:")
                            print(f"   SVG: {svg_file_path}")
                            print(f"   HTML: {html_file_path}")

                            # Update plot display status
                            plot_display.value = "Plot generated - check download"

                            # Open the HTML file in default browser for immediate viewing
                            try:
                                import webbrowser
                                webbrowser.open(f"file://{html_file_path}")
                                print("🌐 Plot opened in default browser")
                            except Exception as browser_error:
                                print(f"⚠️ Could not open browser: {browser_error}")

                        except Exception as e:
                            print(f"⚠️ Could not save plot files: {e}")

                    # Also create ASCII plot for immediate viewing
                    ascii_plot = plotter.create_ascii_plot(plot_data)
                    print("\n📈 ASCII Plot Preview:")
                    print(ascii_plot)

                    # Use a simple status message that's already in the options
                    status_message.value = "Analysis Complete"
                    print("✅ Graph analysis complete - SVG plot generated")
                    print(f"📊 Data summary: {move_count} moves, {len(set(plot_data.get('layer_numbers', [])))} layers")

                    # Print detailed joint analysis
                    joints = plot_data.get('joint_1', [])
                    if joints:
                        print(f"🔧 Joint 1 range: {min(joints):.1f}° to {max(joints):.1f}°")
                        print(f"🔧 Joint 2 range: {min(plot_data.get('joint_2', [0])):.1f}° to {max(plot_data.get('joint_2', [0])):.1f}°")
                        print(f"🔧 Joint 3 range: {min(plot_data.get('joint_3', [0])):.1f}° to {max(plot_data.get('joint_3', [0])):.1f}°")
                else:
                    status_message.value = "No Data Available"
            else:
                status_message.value = "No Data Available"

        except Exception as e:
            status_message.value = "Error"
            print(f"❌ Graph analysis error: {str(e)}")

    def refresh_analysis():
        """Refresh the analysis"""
        refresh_trigger.value += 1
        analyze_data()
    
    # Listen for changes in geo_libs
    if hasattr(calculation_params, 'geo_libs'):
        calculation_params.geo_libs.on_change(lambda _: analyze_data())

    plot_type.on_change(lambda _: analyze_data())
    refresh_trigger.on_change(lambda _: analyze_data())

    return [
        box_ui.drop_down(b, plot_type, text="Analysis Type"),
        ui.button(b, refresh_analysis, label="Analyze Data"),
        box_ui.drop_down(b, status_message, text="Status"),
        box_ui.drop_down(b, plot_display, text="Plot Status"),
        box_ui.file_download(b, plot_file, text="Download Plot (SVG)"),
        ui.label(b, "Graph Analysis: Visualize machine axes vs move points"),
        ui.label(b, "Instructions: 1) Calculate toolpath, 2) Click 'Analyze Data', 3) Plot opens in browser"),
        ui.label(b, "Note: ASCII plot preview and detailed analysis shown in console"),
        ui.label(b, "Features: Joint angle analysis, SVG plotting, HTML display, browser viewing"),
    ]
