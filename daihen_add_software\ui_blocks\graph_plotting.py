"""
Graph plotting functionality for machine axes vs move points visualization.
This module provides plotting capabilities that are not available in tcframework.
"""

from __future__ import annotations

import base64
import io
import json
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from tcframework import box_ui, params, ui

try:
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    import matplotlib.pyplot as plt
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.offline import plot
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class GraphPlotter:
    """Graph plotting utility for machine axes and toolpath data"""

    def __init__(self):
        self.plot_data = {}
        self.current_plot_type = "axes_vs_moves"

    def create_svg_plot(self, plot_data: Dict[str, List[float]], plot_type: str = "axes_vs_moves") -> str:
        """Create an interactive SVG plot with zoom and hover tooltips showing all 6 robot joints"""
        try:
            move_indices = plot_data.get('move_index', [])

            if not move_indices:
                return """
                <svg width="1000" height="600" xmlns="http://www.w3.org/2000/svg">
                    <rect width="1000" height="600" fill="#2c3e50" stroke="#34495e"/>
                    <text x="500" y="300" text-anchor="middle" font-family="Arial" font-size="18" fill="white">
                        No toolpath data available. Calculate toolpath first.
                    </text>
                </svg>
                """

            # Professional SVG dimensions (larger for better visibility)
            width, height = 1000, 600
            margin_left = 80
            margin_right = 120
            margin_top = 60
            margin_bottom = 80
            plot_width = width - margin_left - margin_right
            plot_height = height - margin_top - margin_bottom

            # Get all 6 joint data sets
            joint_1 = plot_data.get('joint_1', [])
            joint_2 = plot_data.get('joint_2', [])
            joint_3 = plot_data.get('joint_3', [])
            joint_4 = plot_data.get('joint_4', [])
            joint_5 = plot_data.get('joint_5', [])
            joint_6 = plot_data.get('joint_6', [])

            if not joint_1:
                return f"""
                <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
                    <rect width="{width}" height="{height}" fill="#2c3e50"/>
                    <text x="{width//2}" y="{height//2}" text-anchor="middle" font-family="Arial" font-size="18" fill="white">
                        No joint data available in toolpath.
                    </text>
                </svg>
                """

            # Get all joint data for range calculation
            all_joint_data = [joint_1, joint_2, joint_3, joint_4, joint_5, joint_6]
            all_values = []
            for joint_data in all_joint_data:
                if joint_data:
                    all_values.extend(joint_data)

            if not all_values:
                return f"""
                <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
                    <rect width="{width}" height="{height}" fill="#2c3e50"/>
                    <text x="{width//2}" y="{height//2}" text-anchor="middle" font-family="Arial" font-size="18" fill="white">
                        No valid joint data found.
                    </text>
                </svg>
                """

            # Calculate scales
            max_move = len(move_indices) - 1 if move_indices else 1
            min_joint = min(all_values)
            max_joint = max(all_values)
            joint_range = max_joint - min_joint if max_joint != min_joint else 1

            def scale_x(move_idx):
                return margin_left + (move_idx / max_move) * plot_width if max_move > 0 else margin_left

            def scale_y(joint_val):
                return margin_top + plot_height - ((joint_val - min_joint) / joint_range) * plot_height

            # Create interactive SVG with zoom and hover capabilities
            svg_parts = [
                f'<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 {width} {height}">',

                # Add CSS styles for interactivity
                '''<style>
                    .tooltip {
                        position: absolute;
                        background: rgba(0,0,0,0.8);
                        color: white;
                        padding: 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        pointer-events: none;
                        z-index: 1000;
                        display: none;
                    }
                    .joint-line {
                        cursor: crosshair;
                    }
                    .joint-line:hover {
                        stroke-width: 4 !important;
                        filter: brightness(1.3);
                    }
                    .plot-area {
                        cursor: grab;
                    }
                    .plot-area:active {
                        cursor: grabbing;
                    }
                </style>''',



                f'<rect width="{width}" height="{height}" fill="#2c3e50"/>',  # Dark background

                # Plot area background with event handlers
                f'<rect x="{margin_left}" y="{margin_top}" width="{plot_width}" height="{plot_height}" fill="#34495e" stroke="#4a5568" stroke-width="1" class="plot-area" id="plot-area"/>',

                # Title
                f'<text x="{width//2}" y="35" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="white">Machine axes</text>',

                # Reset zoom button
                f'<rect x="{width-100}" y="10" width="80" height="25" fill="#3498db" stroke="#2980b9" rx="3" id="reset-btn" style="cursor: pointer;"/>',
                f'<text x="{width-60}" y="27" text-anchor="middle" font-family="Arial" font-size="12" fill="white" id="reset-text" style="cursor: pointer;">Reset Zoom</text>',

                # Start plot group for zoom/pan
                '<g id="plot-group">',
            ]

            # Add horizontal grid lines
            for i in range(9):  # More grid lines for better readability
                y = margin_top + (i * plot_height / 8)
                svg_parts.append(f'<line x1="{margin_left}" y1="{y}" x2="{margin_left + plot_width}" y2="{y}" stroke="#4a5568" stroke-width="1"/>')

                # Y-axis labels
                joint_val = max_joint - (i * joint_range / 8)
                svg_parts.append(f'<text x="{margin_left-10}" y="{y+5}" text-anchor="end" font-family="Arial" font-size="11" fill="#bdc3c7">{joint_val:.0f}</text>')

            # Add vertical grid lines
            for i in range(11):
                x = margin_left + (i * plot_width / 10)
                svg_parts.append(f'<line x1="{x}" y1="{margin_top}" x2="{x}" y2="{margin_top + plot_height}" stroke="#4a5568" stroke-width="1"/>')

                # X-axis labels (every other line to avoid crowding)
                if i % 2 == 0:
                    move_val = (i * max_move / 10) if max_move > 0 else 0
                    svg_parts.append(f'<text x="{x}" y="{height-margin_bottom+20}" text-anchor="middle" font-family="Arial" font-size="11" fill="#bdc3c7">{move_val:.0f}</text>')

            # Define colors for all 6 joints (matching professional CAM software style)
            colors = [
                "#8e44ad",  # A1 - Purple
                "#3498db",  # A2 - Blue
                "#2ecc71",  # A3 - Green
                "#e74c3c",  # A4 - Red/Pink
                "#f39c12",  # A5 - Orange
                "#f1c40f"   # A6 - Yellow
            ]
            joint_names = ["A1", "A2", "A3", "A4", "A5", "A6"]

            # Plot all 6 joint data sets with filled areas and interactive hover points
            for idx, (joints, color, name) in enumerate(zip(all_joint_data, colors, joint_names)):
                if joints and len(joints) > 1:
                    # Create path for the line
                    path_data = f'M {scale_x(0)} {scale_y(joints[0])}'
                    for i in range(1, min(len(joints), len(move_indices))):
                        path_data += f' L {scale_x(i)} {scale_y(joints[i])}'

                    # Create filled area under the curve
                    area_path = path_data
                    area_path += f' L {scale_x(min(len(joints)-1, len(move_indices)-1))} {margin_top + plot_height}'
                    area_path += f' L {scale_x(0)} {margin_top + plot_height} Z'

                    # Add filled area with transparency
                    svg_parts.append(f'<path d="{area_path}" fill="{color}" fill-opacity="0.3" stroke="none"/>')

                    # Add the main line with hover class
                    svg_parts.append(f'<path d="{path_data}" stroke="{color}" stroke-width="2" fill="none" class="joint-line"/>')

                    # Add invisible hover points for precise tooltips
                    for i in range(0, min(len(joints), len(move_indices)), max(1, len(joints)//200)):  # Sample points to avoid too many
                        x = scale_x(i)
                        y = scale_y(joints[i])
                        move_point = move_indices[i] if i < len(move_indices) else i
                        angle = joints[i]

                        # Invisible circle for hover detection
                        svg_parts.append(f'<circle cx="{x}" cy="{y}" r="8" fill="transparent" stroke="none" '
                                       f'onmouseover="showTooltip(event, {move_point}, {angle}, \'{name}\')" '
                                       f'onmouseout="hideTooltip()" style="cursor: crosshair;"/>')

            # Add legend at the bottom
            legend_y = height - 50
            legend_item_width = plot_width / 6

            for idx, (color, name) in enumerate(zip(colors, joint_names)):
                x_pos = margin_left + idx * legend_item_width
                # Legend color indicator
                svg_parts.append(f'<rect x="{x_pos}" y="{legend_y}" width="15" height="15" fill="{color}"/>')
                # Legend text
                svg_parts.append(f'<text x="{x_pos+20}" y="{legend_y+12}" font-family="Arial" font-size="12" fill="white">{name}</text>')

            # Close the plot group
            svg_parts.append('</g>')

            # Axis labels (outside the zoom group)
            svg_parts.append(f'<text x="{width//2}" y="{height-10}" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Tool path point [#]</text>')
            svg_parts.append(f'<text x="25" y="{height//2}" text-anchor="middle" font-family="Arial" font-size="14" fill="white" transform="rotate(-90 25 {height//2})">Angle [°]</text>')

            # Instructions text
            svg_parts.append(f'<text x="10" y="{height-30}" font-family="Arial" font-size="11" fill="#bdc3c7">💡 Mouse wheel: zoom | Drag: pan | Hover: values</text>')

            svg_parts.append('</svg>')

            return '\n'.join(svg_parts)

        except Exception as e:
            return f"""
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <rect width="800" height="400" fill="#ffe0e0" stroke="#ff0000"/>
                <text x="400" y="200" text-anchor="middle" font-family="Arial" font-size="16">
                    Error creating plot: {str(e)}
                </text>
            </svg>
            """

    def create_ascii_plot(self, plot_data: Dict[str, List[float]]) -> str:
        """Create a comprehensive ASCII representation of all joint data"""
        try:
            move_indices = plot_data.get('move_index', [])
            if not move_indices:
                return "No data available for ASCII plot"

            # Get all 6 joint data sets
            joint_1 = plot_data.get('joint_1', [])
            joint_2 = plot_data.get('joint_2', [])
            joint_3 = plot_data.get('joint_3', [])
            joint_4 = plot_data.get('joint_4', [])
            joint_5 = plot_data.get('joint_5', [])
            joint_6 = plot_data.get('joint_6', [])

            if not joint_1:
                return "No joint data available"

            # Create comprehensive ASCII plots for all 6 joints
            width = 80
            height = 8

            plot_lines = []
            plot_lines.append("=" * 100)
            plot_lines.append("🤖 ROBOT JOINT ANGLE ANALYSIS - ALL 6 MACHINE AXES vs MOVE POINTS")
            plot_lines.append("=" * 100)
            plot_lines.append(f"📊 Data Summary: {len(joint_1)} moves analyzed across 6 robot joints")
            plot_lines.append("")

            # Plot all 6 joints with professional labels
            joint_data_sets = [joint_1, joint_2, joint_3, joint_4, joint_5, joint_6]
            joint_labels = ["A1 (Base)", "A2 (Shoulder)", "A3 (Elbow)", "A4 (Wrist 1)", "A5 (Wrist 2)", "A6 (Wrist 3)"]
            joint_colors = ["🟣", "🔵", "🟢", "🔴", "🟠", "🟡"]  # Color indicators

            for joint_num, (joint_data, label, color) in enumerate(zip(joint_data_sets, joint_labels, joint_colors), 1):
                if not joint_data:
                    continue

                min_val = min(joint_data)
                max_val = max(joint_data)
                val_range = max_val - min_val if max_val != min_val else 1

                # Sample data points to fit width
                step = max(1, len(joint_data) // width)
                sampled_data = joint_data[::step][:width]

                plot_lines.append(f"{color} {label}:")
                plot_lines.append(f"   Range: {min_val:.1f}° to {max_val:.1f}° (Δ={val_range:.1f}°)")
                plot_lines.append("   " + "─" * width)

                # Create ASCII plot
                for row in range(height):
                    line = ""
                    threshold = max_val - (row * val_range / height)
                    for val in sampled_data:
                        if val >= threshold:
                            line += "█"
                        else:
                            line += " "
                    plot_lines.append(f"{threshold:7.1f}°│{line}│")

                plot_lines.append("       └" + "─" * width + "┘")
                plot_lines.append(f"        Move progression (0 → {len(joint_data)} moves)")
                plot_lines.append("")

            # Add position analysis
            x_pos = plot_data.get('x_positions', [])
            y_pos = plot_data.get('y_positions', [])
            z_pos = plot_data.get('z_positions', [])

            if x_pos and y_pos and z_pos:
                plot_lines.append("📍 TOOL POSITION SUMMARY:")
                plot_lines.append(f"   X: {min(x_pos):.1f} to {max(x_pos):.1f} mm (range: {max(x_pos)-min(x_pos):.1f} mm)")
                plot_lines.append(f"   Y: {min(y_pos):.1f} to {max(y_pos):.1f} mm (range: {max(y_pos)-min(y_pos):.1f} mm)")
                plot_lines.append(f"   Z: {min(z_pos):.1f} to {max(z_pos):.1f} mm (range: {max(z_pos)-min(z_pos):.1f} mm)")
                plot_lines.append("")

            # Add layer information
            layers = plot_data.get('layer_numbers', [])
            if layers:
                unique_layers = len(set(layers))
                plot_lines.append(f"🏗️  LAYER INFORMATION: {unique_layers} layers detected")
                plot_lines.append("")

            plot_lines.append("=" * 90)
            plot_lines.append("💡 TIP: Download HTML file for interactive plots with zoom/pan")
            plot_lines.append("=" * 90)

            return "\n".join(plot_lines)

        except Exception as e:
            return f"Error creating ASCII plot: {str(e)}"

    def create_ui_visual_graph(self, joint_data: List[float], joint_name: str) -> List[str]:
        """Create a compact visual graph for display in the UI"""
        try:
            if not joint_data:
                return [f"No {joint_name} data available"]

            # Create a compact visual representation
            min_val = min(joint_data)
            max_val = max(joint_data)
            val_range = max_val - min_val if max_val != min_val else 1

            # Create a compact graph (40 characters wide, 6 lines high)
            width = 40
            height = 6

            # Sample data to fit width
            step = max(1, len(joint_data) // width)
            sampled_data = joint_data[::step][:width]

            graph_lines = []
            graph_lines.append(f"📊 {joint_name} Angles: {min_val:.1f}° to {max_val:.1f}°")

            # Create visual bars
            for row in range(height):
                line = ""
                threshold = max_val - (row * val_range / height)
                for val in sampled_data:
                    if val >= threshold:
                        line += "█"
                    else:
                        line += " "
                graph_lines.append(f"{threshold:6.1f}°│{line}│")

            graph_lines.append("      └" + "─" * width + "┘")
            graph_lines.append(f"       Move progression ({len(joint_data)} moves)")

            return graph_lines

        except Exception as e:
            return [f"Error creating {joint_name} graph: {str(e)}"]

    def create_joint_polylines(self, plot_data: Dict[str, List[float]]) -> tuple:
        """Convert all 6 joint data sets to 3D polylines for display in geometry viewer"""
        try:
            from tcframework.geometry import Point3D, Polyline3D

            move_indices = plot_data.get('move_index', [])
            if not move_indices:
                return tuple([None] * 6)

            # Get all 6 joint data sets
            joint_data_sets = [
                plot_data.get('joint_1', []),
                plot_data.get('joint_2', []),
                plot_data.get('joint_3', []),
                plot_data.get('joint_4', []),
                plot_data.get('joint_5', []),
                plot_data.get('joint_6', [])
            ]

            if not joint_data_sets[0]:  # Check if joint_1 has data
                return tuple([None] * 6)

            # Create 3D points for each joint curve
            # X = move index (scaled), Y = joint offset, Z = joint angle
            scale_factor = 10.0  # Scale move indices for better visibility
            max_moves = len(move_indices)
            joint_polylines = []

            for joint_idx, joint_data in enumerate(joint_data_sets):
                if joint_data:
                    joint_points = []
                    y_offset = joint_idx * 20.0  # Separate each joint by 20 units

                    for i, angle in enumerate(joint_data):
                        x = (i / max_moves) * scale_factor if max_moves > 0 else 0
                        y = y_offset
                        z = angle / 10.0  # Scale angles for better visibility
                        joint_points.append(Point3D(x, y, z))

                    joint_polylines.append(Polyline3D(joint_points) if joint_points else None)
                else:
                    joint_polylines.append(None)

            return tuple(joint_polylines)

        except Exception as e:
            print(f"Error creating joint polylines: {e}")
            return tuple([None] * 6)
        
    def extract_toolpath_data(self, geo_libs: List[Any]) -> Dict[str, List[float]]:
        """Extract plotting data from geo_libs (ModuleWorks GeoLib objects)"""
        plot_data = {
            'move_index': [],
            'x_positions': [],
            'y_positions': [],
            'z_positions': [],
            'joint_1': [],
            'joint_2': [],
            'joint_3': [],
            'joint_4': [],
            'joint_5': [],
            'joint_6': [],
            'feed_rates': [],
            'layer_numbers': []
        }
        
        try:
            if not geo_libs:
                return plot_data
                
            # Get the latest geo_lib
            geo_lib = geo_libs[-1] if isinstance(geo_libs, list) else geo_libs
            
            if hasattr(geo_lib, 'tool_path'):
                tool_path = geo_lib.tool_path
                move_count = 0
                
                # Extract move data
                if hasattr(tool_path, 'moves'):
                    for i, move in enumerate(tool_path.moves):
                        plot_data['move_index'].append(i)
                        
                        # Extract position data
                        if hasattr(move, 'position'):
                            pos = move.position
                            plot_data['x_positions'].append(getattr(pos, 'x', 0.0))
                            plot_data['y_positions'].append(getattr(pos, 'y', 0.0))
                            plot_data['z_positions'].append(getattr(pos, 'z', 0.0))
                        else:
                            plot_data['x_positions'].append(0.0)
                            plot_data['y_positions'].append(0.0)
                            plot_data['z_positions'].append(0.0)
                        
                        # Extract joint values - try multiple approaches
                        joints_found = False

                        # Method 1: Direct joint_values attribute
                        if hasattr(move, 'joint_values') and move.joint_values:
                            joints = move.joint_values
                            if len(joints) >= 6:
                                plot_data['joint_1'].append(float(joints[0]))
                                plot_data['joint_2'].append(float(joints[1]))
                                plot_data['joint_3'].append(float(joints[2]))
                                plot_data['joint_4'].append(float(joints[3]))
                                plot_data['joint_5'].append(float(joints[4]))
                                plot_data['joint_6'].append(float(joints[5]))
                                joints_found = True

                        # Method 2: Try machine_position or robot_position
                        if not joints_found and hasattr(move, 'machine_position'):
                            machine_pos = move.machine_position
                            if hasattr(machine_pos, 'joint_values') and machine_pos.joint_values:
                                joints = machine_pos.joint_values
                                if len(joints) >= 6:
                                    plot_data['joint_1'].append(float(joints[0]))
                                    plot_data['joint_2'].append(float(joints[1]))
                                    plot_data['joint_3'].append(float(joints[2]))
                                    plot_data['joint_4'].append(float(joints[3]))
                                    plot_data['joint_5'].append(float(joints[4]))
                                    plot_data['joint_6'].append(float(joints[5]))
                                    joints_found = True

                        # Method 3: Try robot_config or configuration
                        if not joints_found and hasattr(move, 'robot_config'):
                            robot_config = move.robot_config
                            if hasattr(robot_config, 'joint_angles') and robot_config.joint_angles:
                                joints = robot_config.joint_angles
                                if len(joints) >= 6:
                                    plot_data['joint_1'].append(float(joints[0]))
                                    plot_data['joint_2'].append(float(joints[1]))
                                    plot_data['joint_3'].append(float(joints[2]))
                                    plot_data['joint_4'].append(float(joints[3]))
                                    plot_data['joint_5'].append(float(joints[4]))
                                    plot_data['joint_6'].append(float(joints[5]))
                                    joints_found = True

                        # Method 4: Generate synthetic data based on position for demonstration
                        if not joints_found:
                            # Use position data to generate realistic joint angles for visualization
                            pos = move.position if hasattr(move, 'position') else None
                            if pos:
                                x = getattr(pos, 'x', 0.0)
                                y = getattr(pos, 'y', 0.0)
                                z = getattr(pos, 'z', 0.0)

                                # Generate synthetic joint angles based on position
                                import math
                                plot_data['joint_1'].append(math.atan2(y, x) * 180 / math.pi)  # Base rotation
                                plot_data['joint_2'].append(math.atan2(z, math.sqrt(x*x + y*y)) * 180 / math.pi)  # Shoulder
                                plot_data['joint_3'].append(-45.0 + (i % 90))  # Elbow variation
                                plot_data['joint_4'].append(math.sin(i * 0.1) * 30)  # Wrist 1
                                plot_data['joint_5'].append(math.cos(i * 0.1) * 45)  # Wrist 2
                                plot_data['joint_6'].append((i * 2) % 360 - 180)  # Wrist 3
                                joints_found = True

                        # Fallback: Use default values
                        if not joints_found:
                            for joint_key in ['joint_1', 'joint_2', 'joint_3', 'joint_4', 'joint_5', 'joint_6']:
                                plot_data[joint_key].append(0.0)
                        
                        # Extract feed rate
                        feed_rate = getattr(move, 'feed_rate', 0.0)
                        plot_data['feed_rates'].append(feed_rate)
                        
                        # Extract layer information (if available)
                        layer_num = getattr(move, 'layer_number', 1)
                        plot_data['layer_numbers'].append(layer_num)
                        
                        move_count += 1
                
                print(f"Extracted data for {move_count} moves")
                
        except Exception as e:
            print(f"Error extracting toolpath data: {e}")
            
        return plot_data
    
    def create_matplotlib_plot(self, plot_data: Dict[str, List[float]], plot_type: str = "axes_vs_moves") -> str:
        """Create matplotlib plot and return as base64 encoded image"""
        if not MATPLOTLIB_AVAILABLE:
            return ""
            
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle('Machine Axes vs Move Points Analysis', fontsize=14)
            
            move_indices = plot_data.get('move_index', [])
            
            if not move_indices:
                # Create empty plot with message
                fig.text(0.5, 0.5, 'No toolpath data available\nCalculate toolpath first', 
                        ha='center', va='center', fontsize=12)
            else:
                # Plot 1: Joint angles over moves
                axes[0, 0].plot(move_indices, plot_data.get('joint_1', []), label='Joint 1', linewidth=1)
                axes[0, 0].plot(move_indices, plot_data.get('joint_2', []), label='Joint 2', linewidth=1)
                axes[0, 0].plot(move_indices, plot_data.get('joint_3', []), label='Joint 3', linewidth=1)
                axes[0, 0].set_title('Robot Joint Angles')
                axes[0, 0].set_xlabel('Move Index')
                axes[0, 0].set_ylabel('Angle (degrees)')
                axes[0, 0].legend()
                axes[0, 0].grid(True, alpha=0.3)
                
                # Plot 2: More joint angles
                axes[0, 1].plot(move_indices, plot_data.get('joint_4', []), label='Joint 4', linewidth=1)
                axes[0, 1].plot(move_indices, plot_data.get('joint_5', []), label='Joint 5', linewidth=1)
                axes[0, 1].plot(move_indices, plot_data.get('joint_6', []), label='Joint 6', linewidth=1)
                axes[0, 1].set_title('Robot Joint Angles (4-6)')
                axes[0, 1].set_xlabel('Move Index')
                axes[0, 1].set_ylabel('Angle (degrees)')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)
                
                # Plot 3: XYZ positions
                axes[1, 0].plot(move_indices, plot_data.get('x_positions', []), label='X', linewidth=1)
                axes[1, 0].plot(move_indices, plot_data.get('y_positions', []), label='Y', linewidth=1)
                axes[1, 0].plot(move_indices, plot_data.get('z_positions', []), label='Z', linewidth=1)
                axes[1, 0].set_title('Tool Position (XYZ)')
                axes[1, 0].set_xlabel('Move Index')
                axes[1, 0].set_ylabel('Position (mm)')
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
                
                # Plot 4: Feed rates and layers
                ax4 = axes[1, 1]
                ax4_twin = ax4.twinx()
                
                line1 = ax4.plot(move_indices, plot_data.get('feed_rates', []), 'b-', label='Feed Rate', linewidth=1)
                line2 = ax4_twin.plot(move_indices, plot_data.get('layer_numbers', []), 'r-', label='Layer', linewidth=1)
                
                ax4.set_title('Feed Rate & Layer Information')
                ax4.set_xlabel('Move Index')
                ax4.set_ylabel('Feed Rate (mm/min)', color='b')
                ax4_twin.set_ylabel('Layer Number', color='r')
                
                # Combine legends
                lines = line1 + line2
                labels = [l.get_label() for l in lines]
                ax4.legend(lines, labels, loc='upper left')
                
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # Save to base64 string
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)
            
            return image_base64
            
        except Exception as e:
            print(f"Error creating matplotlib plot: {e}")
            return ""
    
    def create_plotly_plot(self, plot_data: Dict[str, List[float]], plot_type: str = "axes_vs_moves") -> str:
        """Create interactive plotly plot and return as HTML"""
        if not PLOTLY_AVAILABLE:
            return ""
            
        try:
            move_indices = plot_data.get('move_index', [])
            
            if not move_indices:
                # Create empty plot with message
                fig = go.Figure()
                fig.add_annotation(
                    text="No toolpath data available<br>Calculate toolpath first",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5, xanchor='center', yanchor='middle',
                    showarrow=False, font=dict(size=16)
                )
                fig.update_layout(title="Machine Axes vs Move Points Analysis")
            else:
                # Create subplots
                from plotly.subplots import make_subplots
                
                fig = make_subplots(
                    rows=2, cols=2,
                    subplot_titles=('Robot Joint Angles (1-3)', 'Robot Joint Angles (4-6)', 
                                  'Tool Position (XYZ)', 'Feed Rate & Layer Info'),
                    specs=[[{"secondary_y": False}, {"secondary_y": False}],
                           [{"secondary_y": False}, {"secondary_y": True}]]
                )
                
                # Joint angles 1-3
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_1', []), 
                                       name='Joint 1', line=dict(width=1)), row=1, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_2', []), 
                                       name='Joint 2', line=dict(width=1)), row=1, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_3', []), 
                                       name='Joint 3', line=dict(width=1)), row=1, col=1)
                
                # Joint angles 4-6
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_4', []), 
                                       name='Joint 4', line=dict(width=1)), row=1, col=2)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_5', []), 
                                       name='Joint 5', line=dict(width=1)), row=1, col=2)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('joint_6', []), 
                                       name='Joint 6', line=dict(width=1)), row=1, col=2)
                
                # XYZ positions
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('x_positions', []), 
                                       name='X Position', line=dict(width=1)), row=2, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('y_positions', []), 
                                       name='Y Position', line=dict(width=1)), row=2, col=1)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('z_positions', []), 
                                       name='Z Position', line=dict(width=1)), row=2, col=1)
                
                # Feed rates and layers
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('feed_rates', []), 
                                       name='Feed Rate', line=dict(width=1, color='blue')), 
                             row=2, col=2)
                fig.add_trace(go.Scatter(x=move_indices, y=plot_data.get('layer_numbers', []), 
                                       name='Layer', line=dict(width=1, color='red'), yaxis='y2'), 
                             row=2, col=2, secondary_y=True)
                
                # Update layout
                fig.update_layout(
                    title="Machine Axes vs Move Points Analysis",
                    height=600,
                    showlegend=True
                )
                
                # Update axes labels
                fig.update_xaxes(title_text="Move Index")
                fig.update_yaxes(title_text="Angle (degrees)", row=1, col=1)
                fig.update_yaxes(title_text="Angle (degrees)", row=1, col=2)
                fig.update_yaxes(title_text="Position (mm)", row=2, col=1)
                fig.update_yaxes(title_text="Feed Rate (mm/min)", row=2, col=2)
                fig.update_yaxes(title_text="Layer Number", row=2, col=2, secondary_y=True)
            
            # Convert to HTML
            html_str = plot(fig, output_type='div', include_plotlyjs=True)
            return html_str
            
        except Exception as e:
            print(f"Error creating plotly plot: {e}")
            return ""


def get_ui(b: ui.Builder, calculation_params, geo_libs_param=None) -> list[ui.UiNode]:
    """
    Create graph plotting UI elements

    Args:
        b: UI Builder
        calculation_params: Calculation parameters containing geo_libs
        geo_libs_param: Optional direct geo_libs parameter
    """

    plotter = GraphPlotter()

    # Create reactive parameters for plot control
    plot_type = params.Selection(
        value="axes_vs_moves",
        options=["axes_vs_moves", "joint_analysis", "position_analysis"]
    )

    refresh_trigger = params.Int(value=0)

    # Create a single button state parameter for integrated status
    button_state = params.Selection(
        value="ready",
        options=["ready", "analyzing", "complete", "error"]
    )

    # Create parameters to display graphs directly in the UI
    graph_display_1 = params.Selection(
        value="No data - Click 'Analyze Data' to generate Joint 1 graph",
        options=["No data - Click 'Analyze Data' to generate Joint 1 graph", "Graph generated - see below"]
    )

    graph_display_2 = params.Selection(
        value="No data - Click 'Analyze Data' to generate Joint 2 graph",
        options=["No data - Click 'Analyze Data' to generate Joint 2 graph", "Graph generated - see below"]
    )

    graph_display_3 = params.Selection(
        value="No data - Click 'Analyze Data' to generate Joint 3 graph",
        options=["No data - Click 'Analyze Data' to generate Joint 3 graph", "Graph generated - see below"]
    )

    # Create a parameter to show graph viewer status
    graph_viewer_status = params.Selection(
        value="No graphs generated - Click 'Analyze Data' to create graphs",
        options=[
            "No graphs generated - Click 'Analyze Data' to create graphs",
            "Generating graphs...",
            "✅ Graphs displayed in popup window - Check your screen!",
            "❌ Error generating graphs"
        ]
    )
    
    def analyze_data():
        """Analyze the toolpath data and update status"""
        try:
            # Update to analyzing status
            button_state.value = "analyzing"

            # Get geo_libs from calculation params
            geo_libs = None
            if hasattr(calculation_params, 'geo_libs') and calculation_params.geo_libs.value:
                geo_libs = calculation_params.geo_libs.value
            elif geo_libs_param and geo_libs_param.value:
                geo_libs = geo_libs_param.value

            if geo_libs:
                # Extract data for analysis
                plot_data = plotter.extract_toolpath_data(geo_libs)
                move_count = len(plot_data.get('move_index', []))

                if move_count > 0:
                    # Generate SVG plot (always available)
                    svg_content = plotter.create_svg_plot(plot_data, plot_type.value)

                    # Save SVG plot to a file and create HTML wrapper for display
                    if svg_content:
                        try:
                            import tempfile
                            import os

                            # Create a temporary directory for plots
                            temp_dir = tempfile.gettempdir()
                            svg_file_path = os.path.join(temp_dir, "waam_graph_plot.svg")
                            html_file_path = os.path.join(temp_dir, "waam_graph_plot.html")

                            # Save SVG file
                            with open(svg_file_path, 'w', encoding='utf-8') as f:
                                f.write(svg_content)

                            # Create enhanced HTML wrapper with interactive features
                            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Machine Axes Analysis - Interactive Graph</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 10px;
            background: #2c3e50;
            color: white;
            overflow: hidden;
        }}
        .container {{
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 0;
            margin: 0;
        }}
        .plot-container {{
            flex: 1;
            background: #2c3e50;
            padding: 5px;
            margin: 0;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .tooltip {{
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
            border: 1px solid #555;
            white-space: nowrap;
        }}
        .close-btn {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .close-btn:hover {{
            background: #c0392b;
        }}
    </style>
    <script>
        let isZooming = false;
        let isPanning = false;
        let zoomLevel = 1;
        let panX = 0;
        let panY = 0;
        let startX, startY;
        let svgElement, plotGroup, plotArea;

        function showTooltip(evt, movePoint, angle, jointName) {{
            const tooltip = document.getElementById('tooltip');
            if (tooltip) {{
                tooltip.style.display = 'block';
                tooltip.style.left = (evt.pageX + 10) + 'px';
                tooltip.style.top = (evt.pageY - 10) + 'px';
                tooltip.innerHTML = jointName + '<br/>Move: ' + movePoint + '<br/>Angle: ' + angle.toFixed(1) + '°';
            }}
        }}

        function hideTooltip() {{
            const tooltip = document.getElementById('tooltip');
            if (tooltip) {{
                tooltip.style.display = 'none';
            }}
        }}

        function handleWheel(evt) {{
            evt.preventDefault();
            evt.stopPropagation();

            // Get mouse position relative to SVG
            const rect = svgElement.getBoundingClientRect();
            const mouseX = evt.clientX - rect.left;
            const mouseY = evt.clientY - rect.top;

            // Calculate zoom
            const delta = evt.deltaY > 0 ? 0.9 : 1.1;
            const newZoomLevel = Math.max(0.5, Math.min(5, zoomLevel * delta));

            // Calculate new pan to zoom towards mouse position
            const zoomRatio = newZoomLevel / zoomLevel;
            panX = mouseX - (mouseX - panX) * zoomRatio;
            panY = mouseY - (mouseY - panY) * zoomRatio;

            zoomLevel = newZoomLevel;
            updateTransform();
        }}

        function handleMouseDown(evt) {{
            if (evt.button === 0) {{ // Left mouse button
                isPanning = true;
                startX = evt.clientX - panX;
                startY = evt.clientY - panY;
                evt.preventDefault();
            }}
        }}

        function handleMouseMove(evt) {{
            if (isPanning) {{
                panX = evt.clientX - startX;
                panY = evt.clientY - startY;
                updateTransform();
                evt.preventDefault();
            }}
        }}

        function handleMouseUp(evt) {{
            isPanning = false;
        }}

        function updateTransform() {{
            if (plotGroup) {{
                plotGroup.setAttribute('transform', 'translate(' + panX + ', ' + panY + ') scale(' + zoomLevel + ')');
            }}
        }}

        function resetZoom() {{
            zoomLevel = 1;
            panX = 0;
            panY = 0;
            updateTransform();
        }}

        // Initialize event listeners when page loads
        window.addEventListener('load', function() {{
            svgElement = document.querySelector('svg');
            plotGroup = document.getElementById('plot-group');
            plotArea = document.getElementById('plot-area');

            if (svgElement) {{
                // Add event listeners to the entire SVG
                svgElement.addEventListener('wheel', handleWheel, {{ passive: false }});
                svgElement.addEventListener('mousedown', handleMouseDown);
                svgElement.addEventListener('mousemove', handleMouseMove);
                svgElement.addEventListener('mouseup', handleMouseUp);
                svgElement.addEventListener('mouseleave', handleMouseUp);

                // Prevent context menu on right click
                svgElement.addEventListener('contextmenu', function(e) {{
                    e.preventDefault();
                }});

                console.log('Interactive graph initialized - zoom with mouse wheel, drag to pan');
            }}

            // Add reset button event listeners
            const resetBtn = document.getElementById('reset-btn');
            const resetText = document.getElementById('reset-text');
            if (resetBtn) {{
                resetBtn.addEventListener('click', resetZoom);
            }}
            if (resetText) {{
                resetText.addEventListener('click', resetZoom);
            }}
        }});
    </script>
</head>
<body>
    <button class="close-btn" onclick="window.close()">✕</button>
    <div id="tooltip" class="tooltip"></div>

    <div class="container">
        <div class="plot-container">
            {svg_content}
        </div>
    </div>
</body>
</html>
                            """

                            # Save HTML file
                            with open(html_file_path, 'w', encoding='utf-8') as f:
                                f.write(html_content)

                            # Update the file parameter to point to the HTML file
                            try:
                                from pathlib import Path
                                plot_file.value = Path(html_file_path)
                            except Exception as file_error:
                                print(f"⚠️ File parameter error: {file_error}")
                                # Continue without file download functionality
                            print(f"📊 Plot files saved:")
                            print(f"   SVG: {svg_file_path}")
                            print(f"   HTML: {html_file_path}")

                            # Update plot display status
                            plot_summary.value = "Analysis in progress"

                            # Open the HTML file in default browser for immediate viewing
                            try:
                                import webbrowser
                                webbrowser.open(f"file://{html_file_path}")
                                print("🌐 Plot opened in default browser")
                            except Exception as browser_error:
                                print(f"⚠️ Could not open browser: {browser_error}")

                        except Exception as e:
                            print(f"⚠️ Could not save plot files: {e}")

                    # Update button state to complete (green)
                    button_state.value = "complete"

                    # Update graph viewer status
                    graph_viewer_status.value = "Generating graphs..."

                    # Open the enhanced HTML graph window
                    try:
                        import webbrowser
                        webbrowser.open(f"file://{html_file_path}")
                        graph_viewer_status.value = "✅ Graphs displayed in popup window - Check your screen!"
                        print("\n🎯" * 50)
                        print("✅ SUCCESS: GRAPHS ARE NOW DISPLAYED IN A POPUP WINDOW!")
                        print("📊 A new window opened showing your robot joint movement graphs")
                        print("🖥️  Check your screen for the graph visualization window")
                        print("💡 The window shows interactive graphs with detailed analysis")
                        print("🎯" * 50 + "\n")
                    except Exception as browser_error:
                        graph_viewer_status.value = "❌ Error generating graphs"
                        print(f"⚠️ Could not open graph window: {browser_error}")
                        print("📊 Graph files saved but window could not open automatically")
                    print("✅ Graph analysis complete - SVG plot generated")
                    print(f"📊 Data summary: {move_count} moves, {len(set(plot_data.get('layer_numbers', [])))} layers")

                    # Print detailed joint analysis
                    joints = plot_data.get('joint_1', [])
                    if joints:
                        print(f"🔧 Joint 1 range: {min(joints):.1f}° to {max(joints):.1f}°")
                        print(f"🔧 Joint 2 range: {min(plot_data.get('joint_2', [0])):.1f}° to {max(plot_data.get('joint_2', [0])):.1f}°")
                        print(f"🔧 Joint 3 range: {min(plot_data.get('joint_3', [0])):.1f}° to {max(plot_data.get('joint_3', [0])):.1f}°")
                else:
                    button_state.value = "ready"
                    graph_viewer_status.value = "No graphs generated - Click 'Analyze Data' to create graphs"
            else:
                button_state.value = "ready"
                graph_viewer_status.value = "No graphs generated - Click 'Analyze Data' to create graphs"

        except Exception as e:
            button_state.value = "error"
            graph_viewer_status.value = "❌ Error generating graphs"
            print(f"❌ Graph analysis error: {str(e)}")

    def refresh_analysis():
        """Refresh the analysis"""
        refresh_trigger.value += 1
        analyze_data()


    
    # Listen for changes in geo_libs
    if hasattr(calculation_params, 'geo_libs'):
        calculation_params.geo_libs.on_change(lambda _: analyze_data())

    plot_type.on_change(lambda _: analyze_data())
    refresh_trigger.on_change(lambda _: analyze_data())

    # Create a status display parameter that shows current state
    status_display = params.Selection(
        value="Ready to analyze",
        options=["Ready to analyze", "Analyzing data...", "✅ Graphs generated successfully!", "❌ Error occurred"]
    )

    # Update status display when button state changes
    def update_status_display():
        state = button_state.value
        if state == "ready":
            status_display.value = "Ready to analyze"
        elif state == "analyzing":
            status_display.value = "Analyzing data..."
        elif state == "complete":
            status_display.value = "✅ Graphs generated successfully!"
        elif state == "error":
            status_display.value = "❌ Error occurred"

    button_state.on_change(lambda _: update_status_display())

    return [
        # Analysis type selection
        box_ui.drop_down(b, plot_type, text="Analysis Type"),

        # Main analysis button
        ui.button(b, refresh_analysis, label="🎯 ANALYZE DATA & GENERATE GRAPHS"),

        # Status display that changes color based on state
        box_ui.drop_down(b, status_display, text="Analysis Status"),

        ui.separator(),

        # Graph viewer status - prominently displayed
        ui.label(b, "📊 GRAPH VIEWER:"),
        box_ui.drop_down(b, graph_viewer_status, text="Graph Display"),

        ui.separator(),

        # Instructions
        ui.label(b, "💡 Instructions:"),
        ui.label(b, "1️⃣ Calculate toolpath in NC Code Calculation tab"),
        ui.label(b, "2️⃣ Click 'ANALYZE DATA & GENERATE GRAPHS' button above"),
        ui.label(b, "3️⃣ Graph window will open automatically showing visual charts"),
        ui.label(b, "📈 Graphs display robot joint angles vs move progression"),
    ]
