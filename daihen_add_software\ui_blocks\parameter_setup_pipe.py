from __future__ import annotations

from dataclasses import dataclass

from tcframework import box_ui
from tcframework import params
from tcframework import storage
from tcframework import ui

PATTERN_OPTIONS = ["ZigZag", "OneWay", "Spiral"]


class ParameterStorage(storage.Node):
    layer_range: params.IntRange = params.IntRange(value=(1, 1), minimum=1, maximum=1000)

    max_angle_between_layers: params.Float = params.Float(value=1.0, minimum=1e-12)

    layer_selection_active_flg: params.Bool = params.Bool(value=True)

    detect_layers_flg: params.Bool = params.Bool(value=False)

    layer_thickness: params.Float = params.Float(value=2.0, minimum=1e-12)

    layer_step_over: params.Float = params.Float(value=2.0, minimum=1e-12)

    offset_to_curve: params.Float = params.Float(value=0.0, minimum=-100.00)

    min_angle_definition: params.Float = params.Float(value=0.0, minimum=-1e-12)

    max_angle_definition: params.Float = params.Float(value=90.0, minimum=1e-12)

    basic_tilt_angle: params.Float = params.Float(value=0.0, minimum=-360.00)

    start_point_rotation_flg: params.Bool = params.Bool(value=False)

    alternate_direction_for_each_layer_flg: params.Bool = params.Bool(value=False)

    pattern: params.Selection = params.Selection(value="ZigZag", options=PATTERN_OPTIONS)

    posting_definition_id: params.Selection = params.Selection(
        value="",
        options=[""]  # Initialize with empty option, will be updated by calculation UI
    )


@dataclass(frozen=True)
class Params:
    layer_range: params.IntRange
    max_angle_between_layers: params.Float
    layer_selection_active_flg: params.Bool
    detect_layers_flg: params.Bool
    start_point_rotation_flg: params.Bool
    alternate_direction_for_each_layer_flg: params.Bool
    layer_thickness: params.Float
    layer_step_over: params.Float
    offset_to_curve: params.Float
    min_angle_definition: params.Float
    max_angle_definition: params.Float
    basic_tilt_angle: params.Float
    pattern: params.Selection
    posting_definition_id: params.Selection

    @staticmethod
    def create(storage: ParameterStorage) -> Params:
        return Params(
            storage.layer_range,
            storage.max_angle_between_layers,
            storage.layer_selection_active_flg,
            storage.detect_layers_flg,
            storage.start_point_rotation_flg,
            storage.alternate_direction_for_each_layer_flg,
            storage.layer_thickness,
            storage.layer_step_over,
            storage.offset_to_curve,
            storage.min_angle_definition,
            storage.max_angle_definition,
            storage.basic_tilt_angle,
            storage.pattern,
            storage.posting_definition_id,
        )


def get_ui(b: ui.Builder, params: Params) -> list[ui.UiNode]:
    params.detect_layers_flg.map(params.layer_selection_active_flg, lambda v: not v)
    # params.start_point_rotation_flg.map(params.start_point_rotation_flg, lambda v: not v)
    return [
        ui.active_if(
            b,
            params.layer_selection_active_flg,
            box_ui.number_range(b, params.layer_range, text="Layer Range"),
        ),
        box_ui.number_field(b, params.max_angle_between_layers, text="Max Angle Between Layers"),
        box_ui.checkbox(b, params.detect_layers_flg, text="Detect Layers"),
        box_ui.number_field(b, params.layer_thickness, text="Layer Thickness"),
        box_ui.number_field(b, params.layer_step_over, text="Layer Step Over"),
        box_ui.number_field(b, params.offset_to_curve, text="Offset to Curve"),
        box_ui.checkbox(b, params.start_point_rotation_flg, text="Start Point Rotation"),
        box_ui.checkbox(
            b,
            params.alternate_direction_for_each_layer_flg,
            text="Alternate Direction for each Layer",
        ),
        box_ui.drop_down(b, params.pattern, text="Pattern"),
        box_ui.number_field(b, params.min_angle_definition, text="Min Angle"),
        box_ui.number_field(b, params.max_angle_definition, text="Max Angle"),
        box_ui.number_field(b, params.basic_tilt_angle, text="Basic Tilt Angle"),
    ]
