<?xml version="1.0" encoding="UTF-8" ?>
<machine_definition>
    <machine_data name="FD-A20_in_Rokko" version="1.9" units="metric" controller="">
        <view_transform initialvalue="1.00000000,0.00000000,0.00000000,0.00000000,0.00000000,1.00000000,0.00000000,0.00000000,0.00000000,0.00000000,1.00000000,0.00000000,0.00000000,0.00000000,0.00000000,1.00000000" />
    </machine_data>
    <axis id="A" type="rotation" x="-0.99995553" y="0.00499978" z="-0.00799964" minvalue="-135.000000" maxvalue="135.000000" valuetype="cont" initial_value="0.000000" rzx="1058.76000977" rzy="-562.09802246" rzz="493.10400391">
        <geometry name="A2-2PF300_Link2" geo="a2-2pf300_link2.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
        <axis id="C" type="rotation" x="-0.00799934" y="0.00999918" z="0.99991804" minvalue="-1000.000000" maxvalue="1000.000000" valuetype="cont" initial_value="0.000000" rzx="1057.92199707" rzy="-561.03002930" rzz="602.09600830">
            <geometry name="A2-2PF300_Link3" geo="a2-2pf300_link3.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
            <transform id="workpiece_transform" initialvalue="1.00000000,0.00000000,0.00000000,1057.59997559,0.00000000,1.00000000,0.00000000,-562.09197998,0.00000000,0.00000000,1.00000000,643.09997559,0.00000000,0.00000000,0.00000000,1.00000000">
                <geometry name="fixture" geo="fixture.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="fixture" />
                <geometry name="initialstock" geo="initialstock.stl" clrr="0.75294119" clrg="0.75294119" clrb="0.75294119" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="initialstock" />
                <geometry name="stock" geo="stock.stl" clrr="0.75294119" clrg="0.75294119" clrb="0.75294119" alpha="0.00" reflectivity="0.40" reflectivityBitmapFileName="" objtype="stock" />
                <geometry name="toolpath" geo="toolpath.asc" clrr="0.80000001" clrg="0.80000001" clrb="0.80000001" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="toolpath" />
                <geometry name="workpiece" geo="workpiece.stl" clrr="0.75294119" clrg="0.75294119" clrb="0.75294119" alpha="0.00" reflectivity="0.40" reflectivityBitmapFileName="" objtype="workpiece" />
            </transform>
        </axis>
    </axis>
    <geometry name="A2-2PF300_Link1" geo="a2-2pf300_link1.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
    <geometry name="FD-A20_Base" geo="fd-a20_base.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
    <axis id="Joint1" type="rotation" x="0.00000000" y="0.00000000" z="1.00000000" minvalue="-170.000000" maxvalue="170.000000" valuetype="cont" initial_value="0.000000" rzx="0.00000000" rzy="0.00000000" rzz="160.00000000">
        <geometry name="FD-A20_Link1" geo="fd-a20_link1.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
        <axis id="Joint2" type="rotation" x="0.00000000" y="-1.00000000" z="0.00000000" minvalue="-65.000000" maxvalue="180.000000" valuetype="cont" initial_value="90.000000" rzx="185.00000000" rzy="-101.00000000" rzz="525.00000000">
            <geometry name="FD-A20_Link2" geo="fd-a20_link2.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
            <axis id="Joint3" type="rotation" x="0.00000000" y="-1.00000000" z="0.00000000" minvalue="-170.000000" maxvalue="190.000000" valuetype="cont" initial_value="0.000000" rzx="945.00000000" rzy="-77.00000000" rzz="525.00000000">
                <geometry name="FD-A20_Link3" geo="fd-a20_link3.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
                <axis id="Joint4" type="rotation" x="0.00000000" y="0.00000000" z="-1.00000000" minvalue="-180.000000" maxvalue="180.000000" valuetype="cont" initial_value="0.000000" rzx="1095.00000000" rzy="0.00000000" rzz="274.00000000">
                    <geometry name="FD-A20_Link4" geo="fd-a20_link4.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
                    <axis id="Joint5" type="rotation" x="0.00000000" y="-1.00000000" z="0.00000000" minvalue="-140.000000" maxvalue="140.000000" valuetype="cont" initial_value="-90.000000" rzx="1095.00000000" rzy="-45.00000000" rzz="-225.00000000">
                        <geometry name="FD-A20_Link5" geo="fd-a20_link5.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
                        <axis id="Joint6" type="rotation" x="0.00000000" y="0.00000000" z="-1.00000000" minvalue="-360.000000" maxvalue="360.000000" valuetype="cont" initial_value="0.000000" rzx="1088.58898926" rzy="5.82800007" rzz="-318.82699585">
                            <geometry name="FD-A20_Link6" geo="fd-a20_link6.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
                            <geometry name="Synchro-Feed-Evo" geo="synchro-feed-evo.stl" clrr="0.50196081" clrg="0.50196081" clrb="0.50196081" alpha="0.00" reflectivity="0.00" reflectivityBitmapFileName="" objtype="geometry" />
                            <transform id="holder_transform" initialvalue="0.63257021,0.00000000,0.77450305,1099.03796387,0.00000000,1.00000000,0.00000000,6.99100018,-0.77450305,0.00000000,0.63257021,-781.18200684,0.00000000,0.00000000,0.00000000,1.00000000">
                                <geometry name="tool" geo="tool.stl" alpha="0.00" reflectivity="0.10" reflectivityBitmapFileName="" objtype="tool" cuttr="0.82745099" cuttg="0.78039217" cuttb="0.67450982" noncuttr="0.50196081" noncuttg="0.50196081" noncuttb="0.50196081" arborr="0.39215687" arborg="0.39215687" arborb="0.39215687" holderr="0.36862746" holderg="0.57254905" holderb="0.69019610" />
                            </transform>
                        </axis>
                    </axis>
                </axis>
            </axis>
        </axis>
    </axis>
    <collcheck id="cc1" name="cc1" group1="Synchro-Feed-Evo" group2="A2-2PF300_Link2,A2-2PF300_Link3,A2-2PF300_Link1" />
    <collcheck id="cc2" name="cc2" group1="Synchro-Feed-Evo" group2="FD-A20_Base,FD-A20_Link2" />
	<post_definition>
        <post_data version="1.0"/>
		<!-- An Empty Post Setting ID needs to be passed while defining of the robot model in the Template CAM  -->		
        <post_settings id="" detection_mode="autodetect_full" machine_type="robotic">
            <machine_dynamics feed_rate="5000.00" rapid_rate="10000.00" tool_change_time="0.00"/>
            <roboticMXPParam>
                <param buffer_mode="buffered"/>
                <param transition_mode="corner_distance"/>
                <param calculation_speed_mode="full"/>
                <param transition_param="0.02"/>
                <StartPosition enable="true">
                    <param is_show_position="true"/>
                    <param mode="direct"/>
                    <param joint_values="A=0,C=0,Joint1=-3.020,Joint2=+91.713,Joint3=-22.399,Joint4=-64.639,Joint5=-62.076,Joint6=+95.149"/>
                    <param is_show_intermediary_positions="false"/>
                </StartPosition>
                <EndPosition enable="false">
                    <param is_show_position="false"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </EndPosition>
                <param enable_feedrate_interpolation="false"/>
                <param enable_collision_checking="false"/>
                <param is_intermediary_positions_on="false"/>
                <param jerk_factor="1"/>
                <RetractAndRewind enable="false" use_group_id="" use_constraint_id="">
                    <param mode_type="limits"/>
                    <param retract_distance="0.3"/>
                    <param rewind_percentage="0.5"/>
                    <param rewinds_percentage=""/>
                    <param limit_avoidance_percentage="0.1"/>
                    <param limits_avoidance_percentage=""/>
                    <param excluded_axis=""/>
                    <param attempts="3"/>
                </RetractAndRewind>
                <FilterDuplicateMoves enable="false">
                    <param type="all"/>
                    <param tolerance="0.00000001"/>
                </FilterDuplicateMoves>
                <CustomCoordinateSystems>
                    <param Name="" rot_x="0" rot_y="0" rot_z="30" shift_x="0" shift_y="0" shift_z="0"/>
                </CustomCoordinateSystems>
                <param named_frames=""/>
                <JointsDynamicLimits fixed_axis_id="" variable_axis_id="">
                    <param values=""/>
                </JointsDynamicLimits>
                <MotionConstraints active_group_id="default" active_constraint_id="default">
                    <Group id="default">
                        <Constraint id="default" type="free_spin_in_z" select_axis_id="Joint6">
                            <param axis_to_sync="C"/>
                            <param is_hard_constraint="true"/>
                            <param speed_factor="1"/>
                            <param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0"/>
                            <param custom_coordinate_system_id="" is_relative="true"/>
                            <MotionParams>
                                <param angular_acceleration_threshold="8"/>
                                <param angular_deceleration_threshold="8"/>
                                <param linear_acceleration_threshold="8"/>
                                <param linear_deceleration_threshold="8"/>
                                <param angular_jerk_threshold="8"/>
                                <param linear_jerk_threshold="8"/>
                                <param angular_velocity_threshold="8"/>
                                <param linear_velocity_threshold="8"/>
                            </MotionParams>
                        </Constraint>
                    </Group>
                </MotionConstraints>
                <PositionControlSystemProperties>
                    <PositionControlSystem>
                        <param time_step_in_sec="0.01"/>
                        <param collision_stopping_threshold="0"/>
                        <param singularity_threshold="0" enable="false"/>
                    </PositionControlSystem>
                    <ControlDefinitions active_id="default">
                        <VelocityControlDefinition id="default">
                            <JointRateFilter>
                                <param apply_acceleration="false"/>
                            </JointRateFilter>
                            <EndEffectorRateFilter>
                                <param end_effector_weights=""/>
                                <param stop_at_axis_limits="true"/>
                                <param stop_at_collisions="false"/>
                                <param check_all_collisions="false"/>
                                <param default_weight="1"/>
                                <param threshold="100000"/>
                            </EndEffectorRateFilter>
                            <MassMatrix>
                                <param mass_matrix=""/>
                            </MassMatrix>
                            <SoftConstraint>
                                <param weight="1"/>
                            </SoftConstraint>
                            <ControlParameters>
                                <param acc_limits=""/>
                                <param deceleration_limits=""/>
                                <param velocity_limits=""/>
                                <param jerk_limits=""/>
                            </ControlParameters>
                            <Scalar>
                                <param scalar="1"/>
                            </Scalar>
                            <Optimizations>
                                <CollisionAvoidance weight="1">
                                    <param avoidance_distance="0.1"/>
                                    <param exponent="3"/>
                                    <param boundary="10"/>
                                    <param check_env_collisions="false"/>
                                    <param check_self_collision="false"/>
                                </CollisionAvoidance>
                                <JointLimitAvoidance weight="-1">
                                    <param excluded_axis=""/>
                                    <param avoidance_zone="0.5"/>
                                    <param exponent="3"/>
                                    <param avoidance_zones=""/>
                                    <param fractional="false"/>
                                    <param maximum="50"/>
                                    <param avoidance_custom_values=""/>
                                </JointLimitAvoidance>
                            </Optimizations>
                        </VelocityControlDefinition>
                    </ControlDefinitions>
                </PositionControlSystemProperties>
            </roboticMXPParam>
        </post_settings>			
		<!-- FREE SPIN IN Z :This post setting id is used as a motion constraint which illustrates as a frame constraint without Z rotation  -->
		<!-- While setting up of the Post Setting ID the user would need to define the following XML tag id's information as illustrated below  -->
		<!-- From the Start Position XML section the user needs to set the joint values which are out of singularity limits--> 
		<!-- Set these values <param joint_values="J1=+0.000,J2=+11.300,J3=+13.100,J4=+3.400,J5=-24.100,J6=+0.000"/>   -->
		<!-- Also dependent of the robot axis id naming the user has to set the select_axis_id tag as illustrated <Constraint id="default" type="frame" select_axis_id="J6"> -->		
        <post_settings id="Free_Spin_In_Z" detection_mode="autodetect_full" machine_type="robotic">
            <machine_dynamics feed_rate="5000.00" rapid_rate="10000.00" tool_change_time="0.00"/>
            <roboticMXPParam>
                <param buffer_mode="buffered"/>
                <param transition_mode="corner_distance"/>
                <param calculation_speed_mode="full"/>
                <param transition_param="0.02"/>
                <StartPosition enable="true">
                    <param is_show_position="true"/>
                    <param mode="direct"/>
                    <param joint_values="A=0,C=0,Joint1=-3.020,Joint2=+91.713,Joint3=-22.399,Joint4=-64.639,Joint5=-62.076,Joint6=+95.149"/>
                    <param is_show_intermediary_positions="false"/>
                </StartPosition>
                <EndPosition enable="false">
                    <param is_show_position="false"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </EndPosition>
                <param enable_feedrate_interpolation="false"/>
                <param enable_collision_checking="false"/>
                <param is_intermediary_positions_on="false"/>
                <param jerk_factor="1"/>
                <RetractAndRewind enable="false" use_group_id="" use_constraint_id="">
                    <param mode_type="limits"/>
                    <param retract_distance="0.3"/>
                    <param rewind_percentage="0.5"/>
                    <param rewinds_percentage=""/>
                    <param limit_avoidance_percentage="0.1"/>
                    <param limits_avoidance_percentage=""/>
                    <param excluded_axis=""/>
                    <param attempts="3"/>
                </RetractAndRewind>
                <FilterDuplicateMoves enable="false">
                    <param type="all"/>
                    <param tolerance="0.00000001"/>
                </FilterDuplicateMoves>
                <CustomCoordinateSystems>
                    <param Name="" rot_x="0" rot_y="0" rot_z="30" shift_x="0" shift_y="0" shift_z="0"/>
                </CustomCoordinateSystems>
                <param named_frames=""/>
                <JointsDynamicLimits fixed_axis_id="" variable_axis_id="">
                    <param values=""/>
                </JointsDynamicLimits>
                <MotionConstraints active_group_id="default" active_constraint_id="default">
                    <Group id="default">
                        <Constraint id="default" type="free_spin_in_z" select_axis_id="Joint6">
                            <param axis_to_sync="C"/>
                            <param is_hard_constraint="true"/>
                            <param speed_factor="1"/>
                            <param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0"/>
                            <param custom_coordinate_system_id="" is_relative="true"/>
                            <MotionParams>
                                <param angular_acceleration_threshold="8"/>
                                <param angular_deceleration_threshold="8"/>
                                <param linear_acceleration_threshold="8"/>
                                <param linear_deceleration_threshold="8"/>
                                <param angular_jerk_threshold="8"/>
                                <param linear_jerk_threshold="8"/>
                                <param angular_velocity_threshold="8"/>
                                <param linear_velocity_threshold="8"/>
                            </MotionParams>
                        </Constraint>
                    </Group>
                </MotionConstraints>
                <PositionControlSystemProperties>
                    <PositionControlSystem>
                        <param time_step_in_sec="0.01"/>
                        <param collision_stopping_threshold="0"/>
                        <param singularity_threshold="0" enable="false"/>
                    </PositionControlSystem>
                    <ControlDefinitions active_id="default">
                        <VelocityControlDefinition id="default">
                            <JointRateFilter>
                                <param apply_acceleration="false"/>
                            </JointRateFilter>
                            <EndEffectorRateFilter>
                                <param end_effector_weights=""/>
                                <param stop_at_axis_limits="true"/>
                                <param stop_at_collisions="false"/>
                                <param check_all_collisions="false"/>
                                <param default_weight="1"/>
                                <param threshold="100000"/>
                            </EndEffectorRateFilter>
                            <MassMatrix>
                                <param mass_matrix=""/>
                            </MassMatrix>
                            <SoftConstraint>
                                <param weight="1"/>
                            </SoftConstraint>
                            <ControlParameters>
                                <param acc_limits=""/>
                                <param deceleration_limits=""/>
                                <param velocity_limits=""/>
                                <param jerk_limits=""/>
                            </ControlParameters>
                            <Scalar>
                                <param scalar="1"/>
                            </Scalar>
                            <Optimizations>
                                <CollisionAvoidance weight="1">
                                    <param avoidance_distance="0.1"/>
                                    <param exponent="3"/>
                                    <param boundary="10"/>
                                    <param check_env_collisions="false"/>
                                    <param check_self_collision="false"/>
                                </CollisionAvoidance>
                                <JointLimitAvoidance weight="-1">
                                    <param excluded_axis=""/>
                                    <param avoidance_zone="0.5"/>
                                    <param exponent="3"/>
                                    <param avoidance_zones=""/>
                                    <param fractional="false"/>
                                    <param maximum="50"/>
                                    <param avoidance_custom_values=""/>
                                </JointLimitAvoidance>
                            </Optimizations>
                        </VelocityControlDefinition>
                    </ControlDefinitions>
                </PositionControlSystemProperties>
            </roboticMXPParam>
        </post_settings>		
		<!-- FRAME :This post setting id is used as a motion constraint which enables the constraining motion of the position as well as the orientation  -->
		<!-- While setting up of the Post Setting ID the user would need to define the following XML tag id's information as illustrated below  -->
		<!-- From the Start Position XML section the user needs to set the joint values which are out of singularity limits--> 
		<!-- Set these values <param joint_values="J1=+0.000,J2=+11.300,J3=+13.100,J4=+3.400,J5=-24.100,J6=+0.000"/>   -->
		<!-- Also dependent of the robot axis id naming the user has to set the select_axis_id tag as illustrated <Constraint id="default" type="frame" select_axis_id="J6"> -->
		<post_settings id="Frame" detection_mode="autodetect_full" machine_type="robotic">
            <machine_dynamics feed_rate="5000.00" rapid_rate="10000.00" tool_change_time="0.00"/>
            <roboticMXPParam>
                <param buffer_mode="buffered"/>
                <param transition_mode="corner_distance"/>
                <param calculation_speed_mode="full"/>
                <param transition_param="0.02"/>
                <StartPosition enable="true">
                    <param is_show_position="true"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </StartPosition>
                <EndPosition enable="false">
                    <param is_show_position="false"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </EndPosition>
                <param enable_feedrate_interpolation="false"/>
                <param enable_collision_checking="false"/>
                <param is_intermediary_positions_on="false"/>
                <param jerk_factor="1"/>
                <RetractAndRewind enable="false" use_group_id="" use_constraint_id="">
                    <param mode_type="limits"/>
                    <param retract_distance="0.3"/>
                    <param rewind_percentage="0.5"/>
                    <param rewinds_percentage=""/>
                    <param limit_avoidance_percentage="0.1"/>
                    <param limits_avoidance_percentage=""/>
                    <param excluded_axis=""/>
                    <param attempts="3"/>
                </RetractAndRewind>
                <FilterDuplicateMoves enable="false">
                    <param type="all"/>
                    <param tolerance="0.00000001"/>
                </FilterDuplicateMoves>
                <CustomCoordinateSystems>
                    <param Name="" rot_x="0" rot_y="0" rot_z="30" shift_x="0" shift_y="0" shift_z="0"/>
                </CustomCoordinateSystems>
                <param named_frames=""/>
                <JointsDynamicLimits fixed_axis_id="" variable_axis_id="">
                    <param values=""/>
                </JointsDynamicLimits>
                <MotionConstraints active_group_id="default" active_constraint_id="default">
                    <Group id="default">
                        <Constraint id="default" type="frame" select_axis_id="">
                            <param axis_to_sync=""/>
                            <param is_hard_constraint="true"/>
                            <param speed_factor="1"/>
                            <param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0"/>
                            <param custom_coordinate_system_id="" is_relative="true"/>
                            <MotionParams>
                                <param angular_acceleration_threshold="8"/>
                                <param angular_deceleration_threshold="8"/>
                                <param linear_acceleration_threshold="8"/>
                                <param linear_deceleration_threshold="8"/>
                                <param angular_jerk_threshold="8"/>
                                <param linear_jerk_threshold="8"/>
                                <param angular_velocity_threshold="8"/>
                                <param linear_velocity_threshold="8"/>
                            </MotionParams>
                        </Constraint>
                    </Group>
                </MotionConstraints>
                <PositionControlSystemProperties>
                    <PositionControlSystem>
                        <param time_step_in_sec="0.01"/>
                        <param collision_stopping_threshold="0"/>
                        <param singularity_threshold="0" enable="false"/>
                    </PositionControlSystem>
                    <ControlDefinitions active_id="default">
                        <VelocityControlDefinition id="default">
                            <JointRateFilter>
                                <param apply_acceleration="false"/>
                            </JointRateFilter>
                            <EndEffectorRateFilter>
                                <param end_effector_weights=""/>
                                <param stop_at_axis_limits="true"/>
                                <param stop_at_collisions="false"/>
                                <param check_all_collisions="false"/>
                                <param default_weight="1"/>
                                <param threshold="100000"/>
                            </EndEffectorRateFilter>
                            <MassMatrix>
                                <param mass_matrix=""/>
                            </MassMatrix>
                            <SoftConstraint>
                                <param weight="1"/>
                            </SoftConstraint>
                            <ControlParameters>
                                <param acc_limits=""/>
                                <param deceleration_limits=""/>
                                <param velocity_limits=""/>
                                <param jerk_limits=""/>
                            </ControlParameters>
                            <Scalar>
                                <param scalar="1"/>
                            </Scalar>
                            <Optimizations>
                                <CollisionAvoidance weight="1">
                                    <param avoidance_distance="0.1"/>
                                    <param exponent="3"/>
                                    <param boundary="10"/>
                                    <param check_env_collisions="false"/>
                                    <param check_self_collision="false"/>
                                </CollisionAvoidance>
                                <JointLimitAvoidance weight="-1">
                                    <param excluded_axis=""/>
                                    <param avoidance_zone="0.5"/>
                                    <param exponent="3"/>
                                    <param avoidance_zones=""/>
                                    <param fractional="false"/>
                                    <param maximum="50"/>
                                    <param avoidance_custom_values=""/>
                                </JointLimitAvoidance>
                            </Optimizations>
                        </VelocityControlDefinition>
                    </ControlDefinitions>
                </PositionControlSystemProperties>
            </roboticMXPParam>
        </post_settings>		
		<!-- COORDINATED JOINT :This post setting id is used as a  motion constraint combined with "Free Spin In Z" or "Frame" enables the constraining motion of the desired Robot Joint, 
		Rotary Positioner Table or the Tilting Positioner Table  -->
		<!-- While setting up of the Post Setting ID the user would need to define the following XML tag id's information as illustrated below  -->
		<!-- From the Start Position XML section the user needs to set the joint values which are out of singularity limits--> 
		<!-- Set these values <param joint_values="J1=+0.000,J2=+11.300,J3=+13.100,J4=+3.400,J5=-24.100,J6=+0.000"/>   -->
		<!-- Also dependent on the robot axis id naming the user has to set the select_axis_id tag as illustrated <Constraint id="default" type="frame" select_axis_id="J6"> -->
		<!-- For the Coordinated Joint definition the user has to also set the select_axis_id for the coordinated joint section tag as illustrated <Constraint id="default" type="coordinated_joint" select_axis_id="J6"> -->
		<!-- The user also needs to define the names of the axis based on the axis id which needs to be blocked in the coordinated constraint section as illustrated 
		<param axis_names="B-Axis, C-Axis" /> -->
		<post_settings id="Frame_Coordinated_Joint_Block_Axis" detection_mode="autodetect_full" machine_type="robotic">
            <machine_dynamics feed_rate="5000.00" rapid_rate="10000.00" tool_change_time="0.00"/>
            <roboticMXPParam>
                <param buffer_mode="buffered"/>
                <param transition_mode="corner_distance"/>
                <param calculation_speed_mode="full"/>
                <param transition_param="0.02"/>
                <StartPosition enable="true">
                    <param is_show_position="true"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </StartPosition>
                <EndPosition enable="false">
                    <param is_show_position="false"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </EndPosition>
                <param enable_feedrate_interpolation="false"/>
                <param enable_collision_checking="false"/>
                <param is_intermediary_positions_on="false"/>
                <param jerk_factor="1"/>
                <RetractAndRewind enable="false" use_group_id="" use_constraint_id="">
                    <param mode_type="limits"/>
                    <param retract_distance="0.3"/>
                    <param rewind_percentage="0.5"/>
                    <param rewinds_percentage=""/>
                    <param limit_avoidance_percentage="0.1"/>
                    <param limits_avoidance_percentage=""/>
                    <param excluded_axis=""/>
                    <param attempts="3"/>
                </RetractAndRewind>
                <FilterDuplicateMoves enable="false">
                    <param type="all"/>
                    <param tolerance="0.00000001"/>
                </FilterDuplicateMoves>
                <CustomCoordinateSystems>
                    <param Name="" rot_x="0" rot_y="0" rot_z="30" shift_x="0" shift_y="0" shift_z="0"/>
                </CustomCoordinateSystems>
                <param named_frames=""/>
                <JointsDynamicLimits fixed_axis_id="" variable_axis_id="">
                    <param values=""/>
                </JointsDynamicLimits>
                <MotionConstraints active_group_id="default" active_constraint_id="default">
                    <Group id="default">
                        <Constraint id="default" type="frame" select_axis_id="">
                            <param axis_to_sync=""/>
                            <param is_hard_constraint="true"/>
                            <param speed_factor="1"/>
                            <param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0"/>
                            <param custom_coordinate_system_id="" is_relative="true"/>
                            <MotionParams>
                                <param angular_acceleration_threshold="8"/>
                                <param angular_deceleration_threshold="8"/>
                                <param linear_acceleration_threshold="8"/>
                                <param linear_deceleration_threshold="8"/>
                                <param angular_jerk_threshold="8"/>
                                <param linear_jerk_threshold="8"/>
                                <param angular_velocity_threshold="8"/>
                                <param linear_velocity_threshold="8"/>
                            </MotionParams>
                        </Constraint>
						<Constraint id="default_2" type="coordinated_joint" select_axis_id="">
								<param axis_to_sync="" />
								<param is_hard_constraint="true" />
								<param speed_factor="1" />
								<param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0" />
								<param custom_coordinate_system_id="" is_relative="true" />
								<param axis_names="" />
								<param independet_axis_names="" />
								<MotionParams>
								<param profile_accelerations="" />
								<param max_velocities="" />
								<param profile_decelerations="" />
								</MotionParams>
						</Constraint>
                    </Group>
                </MotionConstraints>
                <PositionControlSystemProperties>
                    <PositionControlSystem>
                        <param time_step_in_sec="0.01"/>
                        <param collision_stopping_threshold="0"/>
                        <param singularity_threshold="0" enable="false"/>
                    </PositionControlSystem>
                    <ControlDefinitions active_id="default">
                        <VelocityControlDefinition id="default">
                            <JointRateFilter>
                                <param apply_acceleration="false"/>
                            </JointRateFilter>
                            <EndEffectorRateFilter>
                                <param end_effector_weights=""/>
                                <param stop_at_axis_limits="true"/>
                                <param stop_at_collisions="false"/>
                                <param check_all_collisions="false"/>
                                <param default_weight="1"/>
                                <param threshold="100000"/>
                            </EndEffectorRateFilter>
                            <MassMatrix>
                                <param mass_matrix=""/>
                            </MassMatrix>
                            <SoftConstraint>
                                <param weight="1"/>
                            </SoftConstraint>
                            <ControlParameters>
                                <param acc_limits=""/>
                                <param deceleration_limits=""/>
                                <param velocity_limits=""/>
                                <param jerk_limits=""/>
                            </ControlParameters>
                            <Scalar>
                                <param scalar="1"/>
                            </Scalar>
                            <Optimizations>
                                <CollisionAvoidance weight="1">
                                    <param avoidance_distance="0.1"/>
                                    <param exponent="3"/>
                                    <param boundary="10"/>
                                    <param check_env_collisions="false"/>
                                    <param check_self_collision="false"/>
                                </CollisionAvoidance>
                                <JointLimitAvoidance weight="-1">
                                    <param excluded_axis=""/>
                                    <param avoidance_zone="0.5"/>
                                    <param exponent="3"/>
                                    <param avoidance_zones=""/>
                                    <param fractional="false"/>
                                    <param maximum="50"/>
                                    <param avoidance_custom_values=""/>
                                </JointLimitAvoidance>
                            </Optimizations>
                        </VelocityControlDefinition>
                    </ControlDefinitions>
                </PositionControlSystemProperties>
            </roboticMXPParam>
        </post_settings>		
		<!-- GENERAL SPATIAL :This post setting id constraint is used to construct a general constraint that can range from 1 to 6 degrees of freedom. This constraint can be configured to behave as a "point" by 
		configuring the constraint flags to constrain X, Y, Z, in the EE or system frame. It can be configured to behave as a "free_spin_in_z" by configuring the Axis flags to constrain X,Y,Z, Roll, Pitch in the 
		EE frame. To configure the end effector to have the z axis pointing down in the system frame, constrain only the Pitch and Roll in the system frame.  -->
		<!-- While setting up of the Post Setting ID the user would need to define the following XML tag id's information as illustrated below  -->
		<!-- From the Start Position XML section the user needs to set the joint values which are out of singularity limits--> 
		<!-- Set these values <param joint_values="J1=+0.000,J2=+11.300,J3=+13.100,J4=+3.400,J5=-24.100,J6=+0.000"/>   -->
		<!-- Also dependent on the robot axis id naming the user has to set the select_axis_id tag as illustrated <Constraint id="default" type="free_spin_in_z" select_axis_id="J6"> -->
		<!-- For the General Spatial definition the user has to also set the select_axis_id for the coordinated joint section tag as illustrated <Constraint id="default" type="general_spatial" select_axis_id="J6"> -->
		<!-- The user also can define the constraining of the rotation or linear movements along X,Y,Y be setting the constraint_linear_x=true or constraint_angular_x=true as illustrated below 
		<param constraint_linear_x="false" constraint_linear_y="false" constraint_linear_z="false" constraint_angular_x="true" constraint_angular_y="true" constraint_angular_z="false" /> -->
		<post_settings id="General_Spatial" detection_mode="autodetect_full" machine_type="robotic">
            <machine_dynamics feed_rate="5000.00" rapid_rate="10000.00" tool_change_time="0.00"/>
            <roboticMXPParam>
                <param buffer_mode="buffered"/>
                <param transition_mode="corner_distance"/>
                <param calculation_speed_mode="full"/>
                <param transition_param="0.02"/>
                <StartPosition enable="true">
                    <param is_show_position="true"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </StartPosition>
                <EndPosition enable="false">
                    <param is_show_position="false"/>
                    <param mode="direct"/>
                    <param joint_values=""/>
                    <param is_show_intermediary_positions="false"/>
                </EndPosition>
                <param enable_feedrate_interpolation="false"/>
                <param enable_collision_checking="false"/>
                <param is_intermediary_positions_on="false"/>
                <param jerk_factor="1"/>
                <RetractAndRewind enable="false" use_group_id="" use_constraint_id="">
                    <param mode_type="limits"/>
                    <param retract_distance="0.3"/>
                    <param rewind_percentage="0.5"/>
                    <param rewinds_percentage=""/>
                    <param limit_avoidance_percentage="0.1"/>
                    <param limits_avoidance_percentage=""/>
                    <param excluded_axis=""/>
                    <param attempts="3"/>
                </RetractAndRewind>
                <FilterDuplicateMoves enable="false">
                    <param type="all"/>
                    <param tolerance="0.00000001"/>
                </FilterDuplicateMoves>
                <CustomCoordinateSystems>
                    <param Name="" rot_x="0" rot_y="0" rot_z="30" shift_x="0" shift_y="0" shift_z="0"/>
                </CustomCoordinateSystems>
                <param named_frames=""/>
                <JointsDynamicLimits fixed_axis_id="" variable_axis_id="">
                    <param values=""/>
                </JointsDynamicLimits>
                <MotionConstraints active_group_id="default" active_constraint_id="default">
                    <Group id="default">
                        <Constraint id="default" type="free_spin_in_z" select_axis_id="">
                            <param axis_to_sync=""/>
                            <param is_hard_constraint="true"/>
                            <param speed_factor="1"/>
                            <param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0"/>
                            <param custom_coordinate_system_id="" is_relative="true"/>
                            <MotionParams>
                                <param angular_acceleration_threshold="8"/>
                                <param angular_deceleration_threshold="8"/>
                                <param linear_acceleration_threshold="8"/>
                                <param linear_deceleration_threshold="8"/>
                                <param angular_jerk_threshold="8"/>
                                <param linear_jerk_threshold="8"/>
                                <param angular_velocity_threshold="8"/>
                                <param linear_velocity_threshold="8"/>
                            </MotionParams>
                        </Constraint>
						<Constraint id="default_1" type="general_spatial" select_axis_id="">
								<param axis_to_sync="" />
								<param is_hard_constraint="true" />
								<param speed_factor="1" />
								<param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0" />
								<param custom_coordinate_system_id="" is_relative="true" />
								<param constraint_linear_x="false" constraint_linear_y="false" constraint_linear_z="false" constraint_angular_x="false" constraint_angular_y="false" constraint_angular_z="false" />
								<param is_relative_to_ee="true" />
								<param is_actual="true" />
								<MotionParams>
								<param angular_acceleration_threshold="8" />
								<param angular_deceleration_threshold="8" />
								<param linear_acceleration_threshold="8" />
								<param linear_deceleration_threshold="8" />
								<param angular_jerk_threshold="8" />
								<param linear_jerk_threshold="8" />
								<param angular_velocity_threshold="8" />
								<param linear_velocity_threshold="8" />
								</MotionParams>
						</Constraint>
						<Constraint id="default_2" type="coordinated_joint" select_axis_id="">
								<param axis_to_sync="" />
								<param is_hard_constraint="true" />
								<param speed_factor="1" />
								<param changing_placement="true" rot_x="0" rot_y="0" rot_z="0" shift_x="0" shift_y="0" shift_z="0" />
								<param custom_coordinate_system_id="" is_relative="true" />
								<param axis_names="" />
								<param independet_axis_names="" />
								<MotionParams>
								<param profile_accelerations="" />
								<param max_velocities="" />
								<param profile_decelerations="" />
								</MotionParams>
						</Constraint>
                    </Group>
                </MotionConstraints>
                <PositionControlSystemProperties>
                    <PositionControlSystem>
                        <param time_step_in_sec="0.01"/>
                        <param collision_stopping_threshold="0"/>
                        <param singularity_threshold="0" enable="false"/>
                    </PositionControlSystem>
                    <ControlDefinitions active_id="default">
                        <VelocityControlDefinition id="default">
                            <JointRateFilter>
                                <param apply_acceleration="false"/>
                            </JointRateFilter>
                            <EndEffectorRateFilter>
                                <param end_effector_weights=""/>
                                <param stop_at_axis_limits="true"/>
                                <param stop_at_collisions="false"/>
                                <param check_all_collisions="false"/>
                                <param default_weight="1"/>
                                <param threshold="100000"/>
                            </EndEffectorRateFilter>
                            <MassMatrix>
                                <param mass_matrix=""/>
                            </MassMatrix>
                            <SoftConstraint>
                                <param weight="1"/>
                            </SoftConstraint>
                            <ControlParameters>
                                <param acc_limits=""/>
                                <param deceleration_limits=""/>
                                <param velocity_limits=""/>
                                <param jerk_limits=""/>
                            </ControlParameters>
                            <Scalar>
                                <param scalar="1"/>
                            </Scalar>
                            <Optimizations>
                                <CollisionAvoidance weight="1">
                                    <param avoidance_distance="0.1"/>
                                    <param exponent="3"/>
                                    <param boundary="10"/>
                                    <param check_env_collisions="false"/>
                                    <param check_self_collision="false"/>
                                </CollisionAvoidance>
                                <JointLimitAvoidance weight="-1">
                                    <param excluded_axis=""/>
                                    <param avoidance_zone="0.5"/>
                                    <param exponent="3"/>
                                    <param avoidance_zones=""/>
                                    <param fractional="false"/>
                                    <param maximum="50"/>
                                    <param avoidance_custom_values=""/>
                                </JointLimitAvoidance>
                            </Optimizations>
                        </VelocityControlDefinition>
                    </ControlDefinitions>
                </PositionControlSystemProperties>
            </roboticMXPParam>
        </post_settings>
	</post_definition>
</machine_definition>
