from __future__ import annotations

from copy import deepcopy
from dataclasses import dataclass

import moduleworks as mw
from glm import mat4
from tcframework import geometry
from tcframework.geometry import create_mw_mesh_from_stl_file
from tcframework.geometry import create_simple_mesh_from_stl_file
from tcframework.utility import File
from tcframework.utility import part_handler


@dataclass(frozen=True)
class Part:
    mw_mesh: mw.cadcam.TMesh
    # For performance reasons we do not compute a transformed mesh, but use a matrix transform instead:
    display_mesh: geometry.SimpleMesh
    mesh_alignment_transform: mat4 = mat4(1)

    @staticmethod
    def create(stl_file: File, align_position: bool) -> Part:
        if align_position:
            tc_framework_part = part_handler.Part()
            tc_framework_part.stl_file.val = stl_file
            return Part(
                tc_framework_part.mw_mesh.val_checked,
                tc_framework_part.mesh.val_checked,
                tc_framework_part.mesh.transformation.val_checked,
            )
        else:
            return Part(
                create_mw_mesh_from_stl_file(stl_file.path),
                create_simple_mesh_from_stl_file(stl_file.path),
            )


def transform_mw_mesh(mw_mesh: mw.cadcam.TMesh, transform: mat4) -> mw.cadcam.TMesh:
    mesh = deepcopy(mw_mesh)
    mw.cadcam.TMeshService.transform(mesh, mw.cadcam.Matrix4(mw.cadcam.Matrix4_float(transform)))  # type: ignore
    return mesh
