# (C) 2024 ModuleWorks GmbH

import logging
import sys
from pathlib import Path

import click
from tcframework import app
from tcframework import tokens
from tcframework.app import Config
from tcframework.app import run as run_tcframework

from daihen_add_software import settings
from daihen_add_software.custom_settings_template import settings_template

#from .pipe import pipe
from .ppf_adapter import run_post
from .settings import APP_ICON
from .settings import BANNER
from .settings import MAIN_COLOR_HEX
from .settings import POSTING_PORT
from .settings import SECOND_COLOR_HEX
from .settings import STATIC_UI
from .waam_process import waam_process

logger = logging.getLogger("daihen_add_software")


CUSTOMIZATION = app.CustomizationConfig(
    favicon=APP_ICON,
    banner=BANNER,
    style_tokens={
        tokens.BUTTON_PRIMARY_COLOR: MAIN_COLOR_HEX,
        tokens.BUTTON_SECONDARY_COLOR: MAIN_COLOR_HEX,
        tokens.BUTTON_PRIMARY_HOVER: SECOND_COLOR_HEX,
        tokens.BUTTON_SECONDARY_HOVER: SECOND_COLOR_HEX,
        "top-bar-background": MAIN_COLOR_HEX,
        "box-background": SECOND_COLOR_HEX,
    },
)


class ColoredLogFormatter(logging.Formatter):
    grey = "\u001b[30m"
    blue = "\u001b[34m"
    yellow = "\u001b[33m"
    red = "\u001b[31m"
    bold_red = "\x1b[31;1m"
    reset = "\u001b[0m"
    logging_format = "%(asctime)s - %(levelname)s: %(message)s"

    FORMATS = {
        logging.DEBUG: blue + logging_format + reset,
        logging.INFO: grey + logging_format + reset,
        logging.WARNING: yellow + logging_format + reset,
        logging.ERROR: red + logging_format + reset,
        logging.CRITICAL: bold_red + logging_format + reset,
    }

    def format(self, record: logging.LogRecord) -> str:
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, "%Y-%m-%d %H:%M:%S")
        return formatter.format(record)


def run_tcam(backend_only: bool) -> None:
    start_electron = not backend_only
    if start_electron and not sys.platform == "win32":
        logger.warning(
            "UI mode is only supported on Windows. Application is started in backend-only mode. "
            + "Please use a browser to access the UI."
        )
        start_electron = False

    ppf_process = run_post("WARNING", POSTING_PORT, Path("ppf.log"))

    config = Config(
        start_electron=start_electron,
        app_instance_id="daihen_add_software",
        ui_assets=STATIC_UI,
        customization=CUSTOMIZATION,
        autosave_projects=True,
        custom_settings_template=settings_template,
    )
    templates = [waam_process]

    run_tcframework(templates, config)


@click.command(help="Start the posting server")
@click.option("-a", "--keep-alive", is_flag=True, help="Keep posting server running")
@click.option(
    "-p",
    "--port",
    default=POSTING_PORT,
    type=int,
    help="Port on which the posting server is listening",
)
@click.option("-f", "--log-file", default=Path("ppf.log"), help="Log file for posting")
@click.pass_context
def start_post(ctx: click.Context, keep_alive: bool, port: int, log_file: Path) -> None:
    log_level = ctx.obj["LOG_LEVEL"]
    p = run_post(log_level, port, log_file)

    if keep_alive:
        p.join()


@click.command()
@click.option("-i/", "--ui/--no-ui", default=True, help="Start with UI or in backend-mode.")
@click.option("--binfiles/--no-binfiles", default=False, help="Dump binfiles into output folder.")
def run(ui: bool, binfiles: bool) -> None:
    settings.BIN_FILE_DUMP = binfiles
    run_tcam(
        not ui,
    )


@click.group(name="tcam", help="Daihen ADD Software")
@click.option(
    "--log-level",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], case_sensitive=False),
    default="WARNING",
    help="Specify the severity level of logging messages.",
)
@click.pass_context
def main(ctx: click.Context, log_level: str) -> None:
    logging.basicConfig(level=logging.getLevelName(log_level))

    ch = logging.StreamHandler()
    ch.setFormatter(ColoredLogFormatter())
    logger.addHandler(ch)

    ctx.ensure_object(dict)

    ctx.obj["LOG_LEVEL"] = log_level.upper()


main.add_command(run)
main.add_command(start_post)
