# Graph Plotting Functionality for WAAM Template CAM

## Overview
This document describes the newly implemented graph plotting functionality that provides visualization of machine axes vs move points data within the WAAM template CAM application. The system now uses real joint data from CSV files generated by the postprocessor for accurate robot motion visualization.

## Features

### 🎯 **Core Functionality**
- **Machine Joint Analysis**: Visualize all 6 robot joint angles over the toolpath
- **Position Tracking**: Plot X, Y, Z tool positions throughout the moves
- **Feed Rate Analysis**: Monitor feed rate changes across the toolpath
- **Layer Information**: Track layer progression during additive manufacturing
- **Real-time Updates**: Automatically refreshes when toolpath is recalculated

### 📊 **Plotting Methods**

#### 1. **Built-in SVG Plotting** (Always Available)
- **No Dependencies**: Works without external libraries
- **Clean Visualization**: SVG-based plots with joint angle curves
- **Interactive Legend**: Color-coded joint identification
- **Automatic Scaling**: Adapts to data ranges

#### 2. **Enhanced Plotting** (Optional)
- **Matplotlib**: High-quality static plots with subplots
- **Plotly**: Interactive plots with zoom, pan, and hover features
- **Installation**: `pip install matplotlib plotly`

### 🎮 **User Interface**

#### **Location**: New "Graph Analysis" tab in the main UI
#### **Controls**:
- **Analysis Type Dropdown**: Select different analysis views
- **Analyze Data Button**: Process toolpath data and generate analysis
- **Status Dropdown**: Shows current analysis status
- **Instructions**: Step-by-step usage guide
- **Feature Summary**: Overview of available capabilities

### 📈 **Available Plot Types**

#### **1. Axes vs Moves** (Default)
- Joint angles 1-3 in first subplot
- Joint angles 4-6 in second subplot  
- XYZ positions in third subplot
- Feed rate and layer info in fourth subplot

#### **2. Joint Analysis** (Future Enhancement)
- Detailed joint movement analysis
- Joint velocity and acceleration plots
- Singularity detection visualization

#### **3. Position Analysis** (Future Enhancement)
- 3D toolpath visualization
- Position error analysis
- Path smoothness metrics

### 🔧 **Technical Implementation**

#### **Data Extraction**
```python
# Extracts from ModuleWorks GeoLib objects
plot_data = {
    'move_index': [],      # Move sequence numbers
    'x_positions': [],     # Tool X coordinates
    'y_positions': [],     # Tool Y coordinates  
    'z_positions': [],     # Tool Z coordinates
    'joint_1': [],         # Robot joint 1 angles
    'joint_2': [],         # Robot joint 2 angles
    'joint_3': [],         # Robot joint 3 angles
    'joint_4': [],         # Robot joint 4 angles
    'joint_5': [],         # Robot joint 5 angles
    'joint_6': [],         # Robot joint 6 angles
    'feed_rates': [],      # Feed rate values
    'layer_numbers': []    # Layer information
}
```

#### **SVG Generation**
- Pure Python SVG creation
- Automatic scaling and grid generation
- Color-coded joint curves
- Responsive design

### 📋 **Usage Instructions**

#### **Step 1: Setup Toolpath**
1. Configure part setup parameters
2. Set layer range and material properties
3. Calculate NC Code to generate toolpath data

#### **Step 2: Access Graph Analysis**
1. Navigate to the "Graph Analysis" tab
2. The tab appears below the Simulation tab
3. Initial display shows "No toolpath data" message

#### **Step 3: Generate Plots**
1. After toolpath calculation, click "Refresh Plot"
2. Select desired plot type from dropdown
3. View the generated visualization
4. Check "Analysis Summary" for statistical data

#### **Step 4: Enhanced Features** (Optional)
1. Install plotting libraries: `poetry add matplotlib plotly`
2. Restart application
3. Enjoy interactive plots with zoom/pan capabilities

### 📊 **Data Analysis Features**

#### **Statistical Summary**
- Total number of moves
- Joint angle ranges for all 6 axes
- Position ranges (X, Y, Z)
- Automatic updates with toolpath changes

#### **Visual Analysis**
- Joint movement patterns
- Position trajectory visualization
- Feed rate variations
- Layer progression tracking

### 🚀 **Benefits**

#### **For Engineers**
- **Motion Analysis**: Identify problematic joint movements
- **Path Optimization**: Visualize toolpath efficiency
- **Quality Control**: Monitor feed rate consistency
- **Debugging**: Spot issues in robot motion

#### **For Operators**
- **Process Monitoring**: Track manufacturing progress
- **Quality Assurance**: Verify motion parameters
- **Troubleshooting**: Identify motion anomalies

### 🔮 **Future Enhancements**

#### **Planned Features**
- **3D Visualization**: Interactive 3D toolpath display
- **Real-time Monitoring**: Live plotting during simulation
- **Export Capabilities**: Save plots as images/PDFs
- **Custom Analysis**: User-defined plot configurations
- **Comparison Tools**: Compare multiple toolpaths
- **Performance Metrics**: Motion efficiency analysis

#### **Advanced Analytics**
- **Singularity Detection**: Identify robot singularities
- **Collision Analysis**: Visualize potential collisions
- **Optimization Suggestions**: Automated improvement recommendations

### 🛠️ **Installation & Dependencies**

#### **Core Functionality** (Included)
- Built-in SVG plotting
- Data extraction from ModuleWorks
- Statistical analysis

#### **Enhanced Features** (Optional)
```bash
# For enhanced plotting capabilities
poetry add matplotlib plotly

# Or using pip
pip install matplotlib plotly
```

### 📝 **Notes**

- Graph plotting functionality is automatically available in the UI
- No additional configuration required
- Works with existing WAAM toolpath data
- Compatible with all post-processing settings
- Scales automatically with different layer counts

### 🎯 **Integration**

The graph plotting feature is seamlessly integrated into the existing WAAM template CAM workflow:
- **File**: `daihen_add_software/ui_blocks/graph_plotting.py`
- **UI Integration**: Added to `waam_process.py`
- **Dependencies**: Optional matplotlib/plotly for enhanced features
- **Data Source**: Uses existing calculation parameters and geo_libs

This feature addresses the gap in tcframework's plotting capabilities and provides essential visualization tools for robotic additive manufacturing analysis.
