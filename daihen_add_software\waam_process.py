# (C) 2024 ModuleWorks GmbH

from tcframework import app
from tcframework import box_ui
from tcframework import layout
from tcframework import ui

from .storage import Storage
from .ui_blocks import calculation
from .ui_blocks import graph_plotting
from .ui_blocks import parameter_setup
from .ui_blocks import part_setup
from .ui_blocks import simulation
from .ui_blocks import welding_parameters


@app.template(name="Multi-Axis WAAM", tags=["Multi-Axis WAAM", "Daihen"])
def waam_process(b: ui.Builder, c: app.Context) -> ui.UiNode:
    storage = c.get_storage(Storage)
    user_params = parameter_setup.Params.create(storage.parameter_storage)
    part_parameters = part_setup.Params.create(storage.part_storage)
    calculation_params = calculation.Params.create(storage.calculation_storage)
    part_setup_elements = part_setup.get_ui(b, part_parameters)
    parameter_setup_elements = parameter_setup.get_ui(b, user_params)
    welding_parameters_elements = welding_parameters.get_ui(b)
    calculation_elements = calculation.get_ui(
        b, c, calculation_params, part_parameters, user_params, storage.operation
    )
    simulation_elements, simulation_handler = simulation.get_ui(
        b, part_parameters, calculation_params, parameter_inputs=user_params
    )
    graph_plotting_elements = graph_plotting.get_ui(b, calculation_params)

    axes_widget = ui.axes_widget()
    axes_widget_box = ui.column(
        [axes_widget],
        padding=layout.OUTER_SPACING,
        align=ui.Align.START,
    )



    # --- Modernized UI Layout ---
    left = ui.column(
        [
            ui.column(
                [
                    box_ui.widget_box(b, elements=part_setup_elements, title="Part Setup", height="8em"),
                    box_ui.widget_box(b, elements=parameter_setup_elements, title="Parameter Setup", height="6em"),
                    box_ui.widget_box(b, elements=welding_parameters_elements, title="Welding Parameters", height="4em"),
                    box_ui.widget_box(b, elements=calculation_elements, title="NC Code Calculation", height="8em"),
                    box_ui.widget_box(b, elements=simulation_elements, title="Simulation", height="10em"),
                    box_ui.widget_box(b, elements=graph_plotting_elements, title="📊 Graph Plots", height="20em"),
                ],
                gap="0.5em",  # Reduced gap between sections
                padding=layout.OUTER_SPACING,
                grow=True,
            ),
            axes_widget_box,
        ]
    )

    right = layout.spaced_column([
        layout.object_tree_and_axes(),
    ])
    panes = layout.panes_left_right(left, right)
    return layout.geometry_viewer_overlay(
        panes,
        viewer=ui.geometry_viewer(b, simulation_handler.initial_view),
    )
