## Set global paths and functions

Add-Type -AssemblyName System.IO.Compression.FileSystem

# Function to extract zip entries if the file does not already exist
function Expand-ZipSkipExisting {
    param (
        [string]$zipFile,
        [string]$destination
    )

    # Open the zip file
    $zipArchive = [System.IO.Compression.ZipFile]::OpenRead($zipFile)

    foreach ($entry in $zipArchive.Entries) {
        $destinationPath = Join-Path -Path $destination -ChildPath $entry.FullName

        # Skip extraction if the file already exists
        if (-not (Test-Path -Path $destinationPath)) {
            if ($entry.FullName.EndsWith('/')) {
                # It's a directory
                New-Item -ItemType Directory -Path $destinationPath -Force | Out-Null
            }
            else {
                # It's a file
                $destinationDir = [System.IO.Path]::GetDirectoryName($destinationPath)
                if (-not (Test-Path -Path $destinationDir)) {
                    New-Item -ItemType Directory -Path $destinationDir -Force | Out-Null
                }
                
                # Extract the file manually
                $entryStream = $entry.Open()
                $fileStream = [System.IO.File]::Create($destinationPath)
                $entryStream.CopyTo($fileStream)
                $fileStream.Close()
                $entryStream.Close()
            }
        }
        else {
            Write-Host "Skipped existing file: $destinationPath"
        }
    }

    # Close the zip file
    $zipArchive.Dispose()
}
# Folders
$rootFolder = (Resolve-Path ".").Path
$appFolder = "$rootFolder\installer\dist\app"

## Download embedded python

# Create python folder
$pythonPath = "$appFolder\Python"
if (Test-Path -Path $pythonPath) {
    Remove-Item -Recurse -Force -Path $pythonPath
    Write-Host "Deleted existing python folder"
}
New-Item -Path $pythonPath -ItemType Directory
Write-Host "Created new python folder"

# Download embedded python
$pythonUrl = "https://www.python.org/ftp/python/3.10.11/python-3.10.11-embed-amd64.zip"
$pythonZipPath = "$pythonPath\python-3.10.11-embed-amd64.zip"
$pythonDeployPath = $pythonPath
Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonZipPath
Write-Host "Downloaded Python embedded zip to: $pythonZipPath"

# Unpack embedded python
[System.IO.Compression.ZipFile]::ExtractToDirectory($pythonZipPath, $pythonDeployPath)
Write-Host "Unpacked Python embedded zip to: $pythonDeployPath"

# Unpack redistributables
$redistZipPath = "$rootFolder\redist-x64-14.30.30704.0.zip"
Expand-ZipSkipExisting -zipFile $redistZipPath -destination $pythonDeployPath

# Remove temporary files
Remove-Item -Path $pythonZipPath
Write-Host "Removed zip file: $pythonZipPath"


## Download and install wheel files and packages

# Create package folder
$pkgPath = "$appFolder\Pkgs"
if (Test-Path -Path $pkgPath) {
    Remove-Item -Recurse -Force -Path $pkgPath
    Write-Host "Deleted existing package folder"
}
New-Item -Path $pkgPath -ItemType Directory
Write-Host "Created new package folder"

# Generate requirements file and install packages
$requirementsPath = "$rootFolder\requirements.txt"
try {
    poetry export --only=main --with-credentials -o $requirementsPath --without-hashes
    poetry run pip install --target=$pkgPath --no-compile --no-deps -r $requirementsPath
    Write-Host "Successfully installed packages"
}
catch {
    Write-Host "Failed to install packages. Error: $_"
}

# Delete generated requirements.txt
if (Test-Path -Path $requirementsPath) {
    Remove-Item -Force -Path $requirementsPath
    Write-Host "Deleted generated requirements file"
}
else {
    Write-Host "No requirements.txt found"
}

# Copy daihen_add_software package
$daihenAddSoftwarePath = "$appFolder\daihen_add_software"
if (Test-Path -Path $daihenAddSoftwarePath) {
    Remove-Item -Recurse -Force -Path $daihenAddSoftwarePath
    Write-Host "Deleted existing daihen_add_software folder"
}
$postprocessorsPath = "$appFolder\postprocessors"
if (Test-Path -Path $postprocessorsPath) {
    Remove-Item -Recurse -Force -Path $postprocessorsPath
    Write-Host "Deleted existing postprocessors folder"
}
$assetsPath = "$appFolder\assets"
if (Test-Path -Path $assetsPath) {
    Remove-Item -Recurse -Force -Path $assetsPath
    Write-Host "Deleted existing assets folder"
}
Copy-Item -Path "app.py" -Destination "$appFolder\daihen_app.py"
Copy-Item -Path "assets" -Destination "$appFolder\assets" -Recurse -Force
Copy-Item -Path "daihen_add_software" -Destination "$appFolder\daihen_add_software" -Recurse -Force
Copy-Item -Path "postprocessors" -Destination "$appFolder\postprocessors" -Recurse -Force
Copy-Item -Path "mwSupport.lic" -Destination "$pkgPath\moduleworks\mwSupport.lic" -Recurse -Force

# Delete all __pycache__ folders
$pycachePaths = Get-ChildItem -Path $appFolder -Recurse -Directory -Filter '__pycache__'
foreach ($path in $pycachePaths) {
    Remove-Item -Recurse -Force -Path $path.FullName
    Write-Host "Deleted folder: $($path.FullName)"
}

# Delete bin folder
$binPath = "$pkgPath\bin"
if (Test-Path -Path $binPath) {
    Remove-Item -Recurse -Force -Path $binPath
    Write-Host "Deleted folder: $binPath"
}
else {
    Write-Host "Folder $binPath does not exist. Skipping deletion."
}

