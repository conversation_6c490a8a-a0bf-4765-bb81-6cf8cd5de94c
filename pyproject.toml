[tool.poetry]
name = "Daihen ADD Software"
version = "1.0.0"
description = "Daihen Multi-Axis Additive Toolpath Planning & Robot Kinematic Solution"
license = "Proprietary"
authors = [""]
maintainers = [""]
readme = "README.md"
keywords = ["automation"]
packages = [{ include = "daihen_add_software" }]

[tool.poetry.scripts]
app = "daihen_add_software.scripts:main"

[tool.poetry.dependencies]
python = ">=3.10,<3.11"
tcframework = "4.1.8a20"
moduleworks = "2025.4.**********+18eec915bf1d"
click = "^8.1.7"
ppframework = "2.15.0-dev1"
pysimplegui = "4.60.5"
matplotlib = "^3.7.0"
plotly = "^5.17.0"
 

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.2"
pytest-mock = "^3.12.0"
pytest-asyncio = "^0.23.5.post1"


[[tool.poetry.source]]
name = "daihen"
url = "https://pypi.moduleworks.com/simple"
priority = "supplemental"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"
