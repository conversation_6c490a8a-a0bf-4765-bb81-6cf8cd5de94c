stages:
  - deploy
  - installer
variables:
  GIT_SUBMODULE_STRATEGY: recursive
deploy:
  stage: deploy
  image: $CI_REGISTRY/industry/docker-images/pypoetry-win:3.10
  before_script:
    - python -m pip install poetry
    - $CI_JOB_TOKEN | Out-File -FilePath .api-token.txt
    - python -m poetry config http-basic.moduleworks gitlab-ci-token ${CI_JOB_TOKEN}
    - python -m poetry config http-basic.pythonwrapper gitlab-ci-token $CI_JOB_TOKEN   
  script:
    - python -m poetry install
    - powershell -File prepare_installer.ps1
  artifacts:
    paths:
      - installer/dist/app
  tags:
    - docker-windows

installer:
  image: python:3.10-bookworm
  stage: installer
  dependencies:
    - deploy
  needs:
    - deploy
  script:
    - source $MW_SIGNER_BASH_SETUP_FILE
    - apt update && apt install -y nsis
    - makensis ./installer/package.nsi
    - rm ./installer/*.unsigned.exe
  tags:
    - docker-linux-cores-4
  artifacts:
    paths:
      - ./installer/*.exe

