from typing import Dict

from ppframework.pplib.nc_generation.iso import ISONCGenerator
from ppframework.core.types import EulerAngleType


class OTCNCGenerator(ISONCGenerator):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.EndOfProgram.template = ""
        self.NCHeader.header_lines = ""
        self.Program.program_name_spec.extensions = [r"[\d]", ".txt"]
        self.SubProgram.program_name_spec.extensions = [r"[\d]"]
        self.PrimaryDimensionWord.is_modal = False
        self.PrimaryDimensionWord.template = "%.5f"
        self.SequenceNumberWord.template = ""
        self.SequenceNumberWord.sequence_number_start = 3

    class _JointWord(ISONCGenerator.AngularDimensionWord):
        joint_index = 0
        is_modal = False

        def _init_state_model(self):
            return self.nc_generator.controller.active_channel.rotational_axes[self.joint_index]

        def _init_template(self):
            return "%.{precision}f".format(
                precision=self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES)

    class JointR1(_JointWord):
        joint_index = 2

    class JointR2(_JointWord):
        joint_index = 3

    class JointR3(_JointWord):
        joint_index = 4

    class JointR4(_JointWord):
        joint_index = 5

    class JointR5(_JointWord):
        joint_index = 6

    class JointR6(_JointWord):
        joint_index = 7

    class _EulerAngleWord(ISONCGenerator.AtomicNCItem):
        is_modal = False

        def __init__(self, move, **kwargs):
            super().__init__(input=move, **kwargs)
            self.euler_angle_index = self._init_euler_angle_index()

        def _init_template(self):
            raise NotImplementedError

        def _init_euler_angle_index(self):
            raise NotImplementedError

        def _input_hook(self, val):
            return val.euler_angles[self.nc_generator.settings.EULER_ANGLE_TYPE][self.euler_angle_index]

    class EulerAngleX(_EulerAngleWord):
        def _init_template(self):
            return "%.{precision}f".format(precision=self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES)

        def _init_euler_angle_index(self):
            if self.nc_generator.settings.EULER_ANGLE_TYPE is EulerAngleType.RxRyRz:
                return 0  # first entry in list because first rotation
            elif self.nc_generator.settings.EULER_ANGLE_TYPE is EulerAngleType.RzRyRx:
                return 2  # third entry in list because third rotation

    class EulerAngleY(_EulerAngleWord):
        def _init_template(self):
            return "%.{precision}f".format(precision=self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES)

        def _init_euler_angle_index(self):
            return 1

    class EulerAngleZ(_EulerAngleWord):
        def _init_template(self):
            return "%.{precision}f".format(precision=self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES)

        def _init_euler_angle_index(self):
            # passed down from block
            if self.nc_generator.settings.EULER_ANGLE_TYPE is EulerAngleType.RxRyRz:
                return 2  # third entry in list because third rotation
            elif self.nc_generator.settings.EULER_ANGLE_TYPE is EulerAngleType.RzRyRx:
                return 0  # first entry in list because first rotation

    class ComposedNCItem(ISONCGenerator.ComposedNCItem):
        def _handle_comment_and_sequence_numbering(self, **kwargs):
            if self.is_commented:
                self.buffer.append(self.nc_generator.Comment(comment=self.comment).execute())
            if self.buffer and self.has_sequence_numbering:
                result = self.nc_generator.SequenceNumberWord().execute()
                if result:
                    self.buffer.appendleft(result)

    class LinearInterpolationBlock(ComposedNCItem, ISONCGenerator.LinearInterpolationBlock):
        nc_generator: 'OTCNCGenerator'

        children_separator = ","

        def _init_prefix(self):
            if self.nc_generator.settings.IS_JOINT_OUTPUT:
                prefix = "MOVEX A=1,AC=0,SM=0,HM,M1J,L,("
            else:
                prefix = "MOVEX A=1,AC=0,SM=0,HM,M1XW,L,("
            return prefix

        def __init__(self, move, **kwargs):
            axis_names: Dict[str, int] = {'A': None, 'C': None}

            for axis_name in axis_names:
                for i, axis in enumerate(self.nc_generator.controller.machine.axes):
                    if axis_name == axis.uid:
                        axis_names[axis_name] = i

            self.b = move.rotation_axis_values[axis_names['A']]
            self.c = move.rotation_axis_values[axis_names['C']]
            self.c = ((self.c + 180) % 360 + 360) % 360 - 180
            super().__init__(move=move, **kwargs)

        def _init_children(self):
            if self.nc_generator.settings.IS_JOINT_OUTPUT:
                return [self.nc_generator.JointR1,
                        self.nc_generator.JointR2,
                        self.nc_generator.JointR3,
                        self.nc_generator.JointR4,
                        self.nc_generator.JointR5,
                        self.nc_generator.JointR6]
            else:
                return [self.nc_generator.PrimaryDimensionX,
                        self.nc_generator.PrimaryDimensionY,
                        self.nc_generator.PrimaryDimensionZ,
                        self.nc_generator.EulerAngleZ,
                        self.nc_generator.EulerAngleY,
                        self.nc_generator.EulerAngleX]

        def _init_suffix(self):
            return f"),S={self.nc_generator.settings.ROBOT_SPEED},H=3,MS,M2J,P,(" \
                   f" {self.b:.{self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES_POSITIONER}f}," \
                   f"{self.c:.{self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES_POSITIONER}f})," \
                   f"S= {self.nc_generator.settings.ROTARY_TABLE_SPEED}, H=3"

    class JointInterpolationBlock(ComposedNCItem, ISONCGenerator.LinearInterpolationBlock):
        nc_generator: 'OTCNCGenerator'

        children_separator = ","

        def _init_prefix(self):
            return "MOVEX A=1,AC=0,SM=0,HM,M1J,P,("

        def __init__(self, move, **kwargs):
            axis_names: Dict[str, int] = {'A': None, 'C': None}

            for axis_name in axis_names:
                for i, axis in enumerate(self.nc_generator.controller.machine.axes):
                    if axis_name == axis.uid:
                        axis_names[axis_name] = i

            self.b = move.rotation_axis_values[axis_names['A']]
            self.c = move.rotation_axis_values[axis_names['C']]
            self.c = ((self.c + 180) % 360 + 360) % 360 - 180
            super().__init__(move=move, **kwargs)

        def _init_children(self):
            return [self.nc_generator.JointR1,
                    self.nc_generator.JointR2,
                    self.nc_generator.JointR3,
                    self.nc_generator.JointR4,
                    self.nc_generator.JointR5,
                    self.nc_generator.JointR6]

        def _init_suffix(self):
            return f"),S={self.nc_generator.settings.ROBOT_SPEED},H=3,MS,M2J,P,(" \
                   f" {self.b:.{self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES_POSITIONER}f}," \
                   f"{self.c:.{self.nc_generator.settings.NC_TOLERANCE_DECIMAL_PLACES_POSITIONER}f})," \
                   f"S= {self.nc_generator.settings.ROTARY_TABLE_SPEED}, H=3"




