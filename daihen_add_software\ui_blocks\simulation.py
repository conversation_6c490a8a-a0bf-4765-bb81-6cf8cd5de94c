import moduleworks as mw
from glm import vec3
from moduleworks import cncsim
from tcframework import box_ui
from tcframework import geometry
from tcframework import params
from tcframework import ui
from tcframework import utility
from tcframework.utility import simulation_handler
from dataclasses import field
from daihen_add_software import settings

from . import calculation
from . import part_setup


def create_containment_meshes(
    part_params: part_setup.Params, part_meshes: params.List[params.Mesh]
):
    if part_params.containment_meshes_active.value and part_params.containment_meshes.value:
        result: list[params.Mesh] = []
        for part in part_params.containment_meshes.val_checked:
            part_mesh = params.Mesh(part.display_mesh)
            result.append(part_mesh)
        part_meshes.val = result


def get_ui(
    b: ui.Builder,
    part_params: part_setup.Params,
    calculation_params: calculation.Params,
    parameter_inputs=None,
) -> tuple[list[ui.UiNode], simulation_handler.SimulationHandler]:
    simulation = simulation_handler.SimulationHandler()
    part_meshes: params.List[params.Mesh] = params.List(min_length=1, contained_type=params.Mesh)
    guide_mesh: params.Mesh = params.Mesh()
    machsurf_mesh: params.Mesh = params.Mesh()
    #scanned_mesh: params.Mesh = params.Mesh()
    #cad_mesh: params.Mesh = params.Mesh()
    guide_curve_pl = params.Polyline()
    # containment_curve_pl = params.Polyline()

    # --- Toolpath Layer Slider ---
    # Try to determine the number of layers from the toolpath object
    # Fallback to 1 if not available
    num_layers = 1
    if hasattr(simulation.toolpath, 'num_layers'):
        try:
            num_layers = int(simulation.toolpath.num_layers)
        except Exception:
            num_layers = 1
    # You may need to adapt this if your toolpath object exposes layers differently

    current_layer = params.Int(value=1, minimum=1, maximum=num_layers)

    def on_layer_change(new_layer):
        # Placeholder: implement filtering logic if your backend supports it
        # For example, if simulation.toolpath_filtering has a set_layer method:
        if hasattr(simulation.toolpath_filtering, 'set_layer'):
            simulation.toolpath_filtering.set_layer(new_layer)
        # Otherwise, this is a no-op for now
        pass

    current_layer.on_change(on_layer_change)

    def polygonize(crv: mw.CurvArray) -> geometry.SimplePolyline:
        points: list[vec3] = []
        for curve in (crv.get_curve(i) for i in range(crv.size())):
            poly_line = geometry.SimplePolyline.from_mw_3d_polyline(
                mw.CurveAproximator.get_poly_line(curve, 1e-3)
            )
            points.extend(poly_line.points)
        return geometry.SimplePolyline(points)

    part_params.guide_curves.map(guide_curve_pl, mapping=lambda crv: polygonize(crv))
    part_params.guide_mesh.map(guide_mesh, lambda gm: gm.display_mesh)
    part_params.machining_surface.map(machsurf_mesh, lambda ms: ms.display_mesh)
    #part_params.scanned_surface.map(scanned_mesh, lambda ms: ms.display_mesh)
    #part_params.cad_surface.map(cad_mesh, lambda ms: ms.display_mesh)

    def get_workpiece_transform(machine_xml) -> mw.cadcam.Matrix4_float:
        engine = cncsim.create_engine()
        engine.initialize_from_xml(str(machine_xml))  # TODO: remove gedik specific
        workpiece_transform_node = engine.get_node_by_name("workpiece_transform")
        mw_matrix = workpiece_transform_node.global_matrix
        if workpiece_transform_node.id < 0:
            raise Exception("The Machine definition doesn't have a 'workpiece_transform' node.")
        return mw_matrix

    workpiece_transform = params.Mat4()
    workpiece_transform.mw_matrix = get_workpiece_transform(settings.MACHINE_XML)
    simulation.toolpath.transformation.map(workpiece_transform, skip_initial_evaluation=True)
    machine_not_loaded = params.Bool(True)

    def load_machine() -> None:
        simulation.load_machine_from_folder(settings.MACHINE_XML.parent)
        machine_not_loaded.val = False

    # part_params.containment_curves.map(containment_curve_pl, mapping=lambda crv: polygonize(crv))

    # Use an extra parameter so that we are able to clear the reactions for parts without interfering
    # with other reactions of the simulation.toolpath.transformation:

    def update_transformations() -> None:
        full_transformation = (
            workpiece_transform.val_checked * part_params.user_transform.val_checked
        )
        if part_meshes.val and part_params.containment_meshes.val:
            for part_mesh, containment_mesh in zip(
                part_meshes.val, part_params.containment_meshes.val
            ):
                if containment_mesh.mesh_alignment_transform:
                    part_mesh.transformation.val = (
                        full_transformation * containment_mesh.mesh_alignment_transform
                    )

        if part_params.guide_mesh.val:
            guide_mesh.transformation.val = (
                full_transformation * part_params.guide_mesh.val_checked.mesh_alignment_transform
            )
        guide_curve_pl.transformation.value = full_transformation
        if part_params.machining_surface.val:
            machsurf_mesh.transformation.val = (
                full_transformation
                * part_params.machining_surface.val_checked.mesh_alignment_transform
            )

        # if part_params.scanned_surface.val:
        #     scanned_mesh.transformation.val = (
        #         full_transformation
        #         * part_params.scanned_surface.val_checked.mesh_alignment_transform
        #     )

        # if part_params.cad_surface.val:
        #     cad_mesh.transformation.val = (
        #         full_transformation
        #         * part_params.cad_surface.val_checked.mesh_alignment_transform
        #     )

    part_params.containment_meshes.on_any_change(
        lambda _: create_containment_meshes(part_params, part_meshes)
    )
    part_params.user_transform.on_any_change(lambda _: update_transformations())
    part_params.guide_mesh.on_any_change(lambda _: update_transformations())
    part_params.guide_curves.on_any_change(lambda _: update_transformations())
    part_params.machining_surface.on_any_change(lambda _: update_transformations())
    #part_params.scanned_surface.on_any_change(lambda _: update_transformations())
    #part_params.cad_surface.on_any_change(lambda _: update_transformations())

    part_params.containment_meshes.on_any_change(lambda _: update_transformations())
    simulation.toolpath.transformation.on_any_change(lambda _: update_transformations())

    simulation.toolpath.transformation.map(calculation_params.tool_path.transformation)

    def load_mxp(mxp_file: utility.File) -> None:
        if machine_not_loaded.val:
            load_machine()
        simulation.load_mxp_file(mxp_file.path)

    calculation_params.mxp_file.on_change(load_mxp, skip_initial_evaluation=True)

    simulation_playback = simulation_handler.SimulationPlayback(simulation.move_count)
    simulation_playback.drive_simulation(simulation)



    return (
        [
            box_ui.element_row(
                b,
                widget=ui.button(b, load_machine, label="Load Machine", enabled=machine_not_loaded),
            ),
            box_ui.cnc_sim_playback_controls(
                b,
                simulation_playback,
                toolpath_follow_range=simulation.toolpath_filtering.simulation_follow_range,
            ),
            box_ui.cnc_simulation(b, port=simulation.port),
            box_ui.toolpath(
                b,
                simulation.toolpath,
                name="Posted Toolpath",
                toolpath_filtering=simulation.toolpath_filtering,
            ),
            #  box_ui.active_if(
            #     b,
            #     part_params.containment_curves_active,
            #     box_ui.polyline(b, containment_curve_pl, name="Containment Curve"),
            # ),
            box_ui.active_if(
                b,
                part_params.containment_meshes_active,
                box_ui.dynamic_list(
                    b,
                    box_ui.mesh(b, part_meshes, name="Part", color=settings.MAIN_COLOR_RGB),
                    part_meshes,
                ),
            ),
            box_ui.mesh(b, machsurf_mesh, name="Machining Surface"),
            #box_ui.mesh(b, scanned_mesh, name="Scanned Surface Geometry"),
            #box_ui.mesh(b, cad_mesh, name="CAD Surface Geometry"),
            box_ui.active_if(
                b,
                part_params.guide_curves_active,
                box_ui.tube(b, polyline=guide_curve_pl, name="Guide Curve"),
            ),
            box_ui.active_if(
                b, part_params.guide_mesh_active, box_ui.mesh(b, guide_mesh, name="Guide Mesh")
            ),
        ],
        simulation,
    )
