from __future__ import annotations

from tcframework import app
from tcframework import box_ui
from tcframework import params
from tcframework import ui
from tcframework import utility


def t(key: str) -> utility.TranslationKey:
    return utility.t(f"app.settings.{key}")


@app.template(name="New Settings template", tags=["settings"])
def settings_template(b: ui.Builder, c: app.Context) -> ui.UiNode:
    has_custom_app_info = params.Bool()
    custom_app_name = params.Str()
    custom_app_version = params.Str()
    version_info = c.get_version_info()
    tcframework_version = params.Str(version_info.tcframework_version)
    moduleworks_version = params.Str(version_info.moduleworks_version)
    if version_info.custom_version_info is None:
        has_custom_app_info.value = False
    else:
        has_custom_app_info.value = True
        custom_app_name.value = version_info.custom_version_info.custom_app_name
        custom_app_version.value = version_info.custom_version_info.custom_app_version

    license_file = c.get_license_file()

    elements = [
        box_ui.text_label(b, text="Info", param="This is a fully custom settings page!"),
        box_ui.active_if(
            b, has_custom_app_info, box_ui.text_label(b, custom_app_version, text=custom_app_name)
        ),
        box_ui.text_label(b, tcframework_version, text=t("label.framework_version")),
        box_ui.text_label(b, moduleworks_version, text=t("label.moduleworks_version")),
        box_ui.element_row(b, t("label.locale"), ui.locale_selection()),
        box_ui.file_upload(
            b,
            license_file,
            text=t("button.license-upload"),
        ),
        ui.separator(),
        box_ui.element_row(
            b,
            t("label.dark_mode"),
            ui.row([ui.settings_dark_mode()], align=ui.Align.END),
        ),
        ui.row([ui.label(b, t("label.camera_settings"))], align=ui.Align.START),
        box_ui.element_row(
            b,
            t("label.invert_scroll_direction"),
            ui.settings_checkbox(kind=ui.SettingsKind.INVERT_SCROLL_DIRECTION),
        ),
        box_ui.element_row(
            b,
            t("label.fixed_up_axis"),
            ui.settings_checkbox(kind=ui.SettingsKind.FIXED_UP_AXIS),
        ),
        box_ui.element_row(b, t("label.input_mode"), ui.settings_input_mode()),
        box_ui.element_row(b, t("label.download_logs"), ui.settings_download_logs()),
    ]

    return ui.column(elements)
