# Daihen ADD Software

# TemplateCAM for Daihen

## Prerequisites

### Python

[Python](https://www.python.org) is required in minium version 3.8 and maximum version 3.10. If no such version is
available on your system, use the [official download](https://www.python.org/downloads/) page to download an installer
for your system. Use this installer to install python to your system.

### Poetry

[Poetry](https://python-poetry.org/) is used as dependency management system for the project. Please use the
[official installation instructions](https://python-poetry.org/docs/#installation) to install it on your system.

After installing poetry, you should be able to run `poetry --version` without any errors.

## Installation instructions

To use the customized TemplateCAM application, you need an access token from the customer pypi. Therefore, follow the instructions: https://wiki.moduleworks.com/spaces/IP/pages/1555759900/How+to+add+a+new+customer **step 3** to get the password for daihen.

Create a new file `api-token.txt` (that specific name) and save the token in this file. This is required to get access
to the posting framework in a later step.

Further, prepare the project by running the following command:

```bash
poetry config http-basic.daihen daihen <your-token> 
```

Replace `<your-token>` by the generated access token. This will make the GitLab package registry available to the
project.

Now, run

```bash
poetry install

```

to set up the project.

For finding out the correction version for ppframework, currently you need to enter any version (e.g. 2.13.0), run the postprocessor from the script and see in the error-message which version is necessary.

At this point, you have a complete TemplateCAM setup.

## Use TemplateCAM

The entry point for TemplateCAM is the `app` script. To run it from the project, run `poetry run app run`. It will start
the UI.  

## Build binary package

To create a Windows binary package run

```bash
poetry run tcbuild
```

This creates a new folder `dist\app`. Inside this folder, you find an application called `app.exe`. You can use this
application instead of running `poetry run app`. You can distribute the folder as standalone application for users
which do not have access to `python` and/or `poetry`.

You can use `app.exe` also in head-less mode. Therefore, start it from the terminal. You get the same help messages if
you run `app.exe -h`.
