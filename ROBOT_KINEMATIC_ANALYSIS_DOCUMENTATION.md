# Robot Kinematic Analysis Documentation

## Overview

This document provides comprehensive documentation for the robot kinematic analysis system implemented in the Daihen Template CAM Multi-Axis Additive application. The system calculates and visualizes joint velocities, accelerations, and jerks using advanced mathematical formulas based on professional robotics analysis standards.

## Mathematical Formulas and Implementation

### 1. Wrap-Around Difference Calculation

**Purpose**: Handle 0-360° joint angle wrap-around for continuous motion analysis.

**Formula**:
```
Δθ_k = ((θ_{k+1} - θ_k + 180) % 360) - 180  [degrees]
```

**Implementation Location**: 
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_time_intervals()`
- Lines: ~540-560

**Code**:
```python
# Wrap-around difference calculation
diff = ((q_deg[i, j] - q_deg[i-1, j] + 180) % 360) - 180
```

### 2. Trajectory Unwrapping

**Purpose**: Create continuous joint trajectories without 360° jumps.

**Formula**:
```
θ_unw[k+1] = θ_unw[k] + Δθ_k
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_time_intervals()`
- Lines: ~540-560

**Code**:
```python
# Trajectory unwrapping
q_unw[1:, j] = q_unw[0, j] + np.cumsum(diffs)
```

### 3. Joint-Space Distance Calculation

**Purpose**: Calculate Euclidean distance in joint space for realistic timing.

**Formula**:
```
d_k = ‖q_unw[k+1] – q_unw[k]‖₂  [degrees]
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_time_intervals()`
- Lines: ~540-560

**Code**:
```python
# Joint-space distance calculation
dq = np.diff(q_unw, axis=0)
dist = np.linalg.norm(dq, axis=1)  # Euclidean distance
```

### 4. Segment Time Calculation

**Purpose**: Calculate realistic time intervals based on joint motion and feedrate.

**Formula**:
```
Δt_k = (d_k / F) × 60  [seconds]
where F = 1500 mm/min (feedrate)
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_time_intervals()`
- Lines: ~540-560

**Code**:
```python
# Segment time calculation
dt_min = dist / feedrate_mm_per_min  # minutes
dt_s = dt_min * 60.0  # convert to seconds
```

### 5. Cumulative Time Vector

**Purpose**: Create time array for derivative calculations.

**Formula**:
```
t[0] = 0
t[k+1] = t[k] + Δt_k
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_joint_velocities()`
- Lines: ~650-680

**Code**:
```python
# Cumulative time vector
dt_s = np.array(time_intervals)
t = np.insert(np.cumsum(dt_s), 0, 0.0)  # Time vector
```

### 6. Central Difference Derivatives

**Purpose**: Calculate smooth, accurate derivatives using central differences.

**Formulas**:

**Interior Points (Most Accurate)**:
```
ẏ_i = (y_{i+1} - y_{i-1}) / (t_{i+1} - t_{i-1})
```

**Endpoints (Forward/Backward)**:
```
ẏ[0] = (y[1] - y[0]) / (t[1] - t[0])          # Forward difference
ẏ[-1] = (y[-1] - y[-2]) / (t[-1] - t[-2])     # Backward difference
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_central_diff()`
- Lines: ~565-585

**Code**:
```python
def _central_diff(self, y, t):
    """First derivative via central differences"""
    dy = np.zeros_like(y)
    
    # Endpoints
    dy[0] = (y[1] - y[0]) / (t[1] - t[0])
    dy[-1] = (y[-1] - y[-2]) / (t[-1] - t[-2])
    
    # Interior points (central difference)
    if len(y) > 2:
        if y.ndim == 1:
            dy[1:-1] = (y[2:] - y[:-2]) / (t[2:] - t[:-2])
        else:
            dy[1:-1] = (y[2:] - y[:-2]) / (t[2:, None] - t[:-2, None])
    
    return dy
```

## Kinematic Calculations

### 1. Velocity Calculation

**Purpose**: Calculate joint angular velocities.

**Formula**:
```
v = dθ/dt  [rad/s] → converted to [°/s] for display
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_joint_velocities()`
- Lines: ~630-705

**Process**:
1. Apply wrap-around unwrapping to joint angles
2. Convert degrees to radians
3. Create cumulative time vector
4. Apply central difference derivative
5. Convert back to degrees for display
6. Optional Savitzky-Golay smoothing

### 2. Acceleration Calculation

**Purpose**: Calculate joint angular accelerations.

**Formula**:
```
a = dv/dt  [rad/s²] → converted to [°/s²] for display
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_joint_accelerations()`
- Lines: ~707-755

**Process**:
1. Take velocity data from previous calculation
2. Apply central difference derivative to velocities
3. Convert back to degrees for display
4. Optional Savitzky-Golay smoothing

### 3. Jerk Calculation

**Purpose**: Calculate joint angular jerks (rate of change of acceleration).

**Formula**:
```
j = da/dt  [rad/s³] → converted to [°/s³] for display
```

**Implementation Location**:
- File: `daihen_add_software/ui_blocks/graph_plotting.py`
- Method: `_calculate_joint_jerks()`
- Lines: ~757-805

**Process**:
1. Take acceleration data from previous calculation
2. Apply central difference derivative to accelerations
3. Convert back to degrees for display
4. Optional Savitzky-Golay smoothing

## Data Flow and Processing Steps

### Step 1: Data Input
- **Source**: CSV file with joint angles from NC code calculation
- **Format**: 8 columns (Joint_1 to Joint_6, Positioner_A, Positioner_C)
- **Units**: Degrees

### Step 2: Time Interval Calculation
1. **Wrap-around handling**: Apply 360° wrap-around difference
2. **Trajectory unwrapping**: Create continuous joint paths
3. **Distance calculation**: Compute joint-space Euclidean distances
4. **Time calculation**: Convert distances to time using feedrate

### Step 3: Kinematic Analysis
1. **Velocity**: Central difference of position vs time
2. **Acceleration**: Central difference of velocity vs time  
3. **Jerk**: Central difference of acceleration vs time

### Step 4: Data Processing
1. **Unit conversion**: Radians to degrees for user display
2. **Optional smoothing**: Savitzky-Golay filter (if SciPy available)
3. **Data formatting**: Convert to dictionary format for plotting

### Step 5: Visualization
1. **Graph generation**: Plotly interactive plots
2. **Smooth curves**: Line plots with smoothing parameter
3. **Interactive features**: Zoom, pan, hover tooltips

## Code Architecture

### Main Files and Components

#### 1. Graph Plotting Module
**File**: `daihen_add_software/ui_blocks/graph_plotting.py`

**Key Classes**:
- `GraphPlottingBlock`: Main class handling all graph operations

**Key Methods**:
- `_calculate_time_intervals()`: Time calculation with wrap-around
- `_central_diff()`: Central difference derivative calculation
- `_calculate_joint_velocities()`: Velocity analysis
- `_calculate_joint_accelerations()`: Acceleration analysis
- `_calculate_joint_jerks()`: Jerk analysis
- `_apply_smoothing()`: Optional Savitzky-Golay smoothing
- `_convert_radians_to_degrees()`: Unit conversion for display

#### 2. Main Application Integration
**File**: `daihen_add_software/waam_process.py`

**Integration Point**:
```python
graph_plotting_elements = graph_plotting.get_ui(b, calculation_params)
```

**UI Layout**:
- Graph plotting section in left panel
- 4 graph types: Joint Angles, Velocities, Accelerations, Jerks
- Interactive controls for joint visibility

### Dependencies

#### Required Libraries
- **NumPy**: Matrix operations and mathematical calculations
- **Plotly**: Interactive graph generation
- **SciPy** (Optional): Savitzky-Golay smoothing filter

#### Data Sources
- **Input**: `robot_joint_values.csv` from NC code calculation
- **Output**: Interactive HTML graphs served via local web server

## Graph Types and Features

### 1. Joint Angles vs Move Points
- **X-axis**: Move index (discrete points)
- **Y-axis**: Joint angles (degrees)
- **Purpose**: Position analysis throughout toolpath

### 2. Joint Velocities vs Time
- **X-axis**: Time (seconds)
- **Y-axis**: Angular velocity (°/s)
- **Purpose**: Speed profile analysis
- **Features**: Smooth curves, central differences

### 3. Joint Accelerations vs Time
- **X-axis**: Time (seconds)
- **Y-axis**: Angular acceleration (°/s²)
- **Purpose**: Dynamic load analysis
- **Features**: Smooth curves, central differences

### 4. Joint Jerks vs Time
- **X-axis**: Time (seconds)
- **Y-axis**: Angular jerk (°/s³)
- **Purpose**: Motion smoothness analysis
- **Features**: Smooth curves, central differences

## Configuration Parameters

### Feedrate Settings
- **Default**: 1500 mm/min
- **Location**: `_calculate_time_intervals()` method
- **Purpose**: Realistic timing calculation

### Smoothing Parameters
- **Window Size**: 7 points (must be odd, ≥5)
- **Polynomial Order**: 3
- **Location**: `_apply_smoothing()` method
- **Purpose**: Noise reduction in kinematic data

### Wrap-Around Settings
- **Period**: 360 degrees
- **Location**: `_calculate_time_intervals()` method
- **Purpose**: Handle rotary joint motion

## Usage in Application

### User Workflow
1. **Calculate Toolpath**: Generate NC code with joint angles
2. **Select Graph Type**: Choose from 4 available graph types
3. **Generate Analysis**: Click "ANALYZE DATA & GENERATE GRAPHS"
4. **View Results**: Click "OPEN INTERACTIVE GRAPH"
5. **Interact**: Use zoom, pan, hover, and joint visibility controls

### Technical Workflow
1. **Data Loading**: Read joint angles from CSV file
2. **Time Calculation**: Apply wrap-around and distance-based timing
3. **Kinematic Analysis**: Calculate derivatives using central differences
4. **Data Processing**: Convert units and apply smoothing
5. **Visualization**: Generate interactive Plotly graphs
6. **Serving**: Host graphs on local web server

## Performance Considerations

### Computational Efficiency
- **NumPy vectorization**: Fast matrix operations for 8-joint analysis
- **Memory management**: Efficient array handling for large datasets
- **Caching**: Reuse calculations when possible

### Accuracy Considerations
- **Central differences**: Higher accuracy than forward/backward differences
- **Wrap-around handling**: Prevents artificial velocity spikes
- **Smoothing**: Optional noise reduction without losing important features

## Error Handling

### Common Issues and Solutions
1. **Missing NumPy**: Graceful fallback with error message
2. **Missing SciPy**: Skip smoothing, continue with unsmoothed data
3. **Insufficient data**: Minimum 2 points required for analysis
4. **Zero motion**: Handle stationary segments with minimum time values

### Debugging Features
- **Console output**: Progress messages and warnings
- **Error logging**: Detailed error messages for troubleshooting
- **Data validation**: Check for valid input data before processing

## Future Enhancements

### Potential Improvements
1. **Adaptive smoothing**: Automatic parameter selection
2. **Multiple feedrates**: Support for varying speeds
3. **Advanced filtering**: Additional noise reduction methods
4. **Export capabilities**: Save analysis results to files
5. **Comparison tools**: Compare multiple toolpaths

This documentation provides a complete reference for understanding, maintaining, and extending the robot kinematic analysis system.
