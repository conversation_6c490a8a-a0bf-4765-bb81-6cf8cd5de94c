# (C) 2024 ModuleWorks GmbH

import logging
import multiprocessing as mp
import time
from pathlib import Path
from threading import Thread

from ppframework.pplib.nc_generation.writer import NCCodeFileWriter  # type: ignore

from daihen_add_software.settings import OUTPUT_DIR
from postprocessors.post_setup.otc_nc_generator import OTCNCGenerator
from postprocessors.post_setup.otc_post import OTCPostProcessor  # type: ignore
from postprocessors.post_setup.otc_post import OTCSettings

logger = logging.getLogger("tcam")


def run_for_one_file(log_level: str, port: int, log_file: Path, output_dir: Path) -> None:
    settings = OTCSettings(
        LOG_FILE=str(log_file),
        LOG_LEVEL=logging.getLevelName(log_level),
        PORT=port,
        LOG_TO_CONSOLE=False,
        NC_HAS_UNIQUE_SUBFOLDER=False,
        NC_HAS_ONE_PROGRAM_PER_OPERATION=False,
        NC_TOLERANCE_DECIMAL_PLACES=3,
        NC_OUTPUT_DIR=output_dir,
    )

    nc_gen = OTCNCGenerator(settings=settings)
    writer = NCCodeFileWriter(settings, nc_generator=nc_gen)
    post_processor = OTCPostProcessor(settings, nc_gen, writer)
    post_processor.run()


def f(log_level: str, port: int, log_file: Path) -> None:
    while True:
        output = OUTPUT_DIR / f"nc_{time.strftime('%Y%m%d-%H%M%S')}"
        try:
            run_for_one_file(log_level, port, log_file, output)
        except Exception as e:
            logger.error(e)


def run_post_loop(log_level: str, port: int, log_file: Path) -> None:
    # When MW Postprocessors gets wrong inputs - it kills the process
    # Therefore if it gets killed we start a new process

    while True:
        p = mp.Process(target=f, args=(log_level, port, log_file), daemon=True)
        p.start()
        logger.debug(f"Posting server is running with process id {p.pid}")
        p.join()


def run_post(log_level: str, port: int, log_file: Path) -> Thread:
    logger.info(f"Start posting server on port {port}")
    logger.info(f"PPF logs are written to {log_file}")
    thread = Thread(target=run_post_loop, args=(log_level, port, log_file), daemon=True)
    thread.start()

    return thread
