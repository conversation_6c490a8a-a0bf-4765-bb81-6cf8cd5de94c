from __future__ import annotations

from dataclasses import dataclass

import moduleworks as mw
from tcframework import app
from tcframework import box_ui
from tcframework import geometry
from tcframework import operation
from tcframework import params
from tcframework import storage
from tcframework import ui

from ..operations.posting import PostingOperation
from ..operations.posting import PostingOperationInputs
from ..operations.posting import PostingOperationOutputs
from ..operations.tool_path_generation_pipe import ToolPathCalculation
from ..operations.tool_path_generation_pipe import ToolPathOperationInputs
from ..operations.tool_path_generation_pipe import ToolPathOperationOutputs
from . import parameter_setup_pipe
from . import part_setup_pipe

GUIDE_MAPPING = {
    part_setup_pipe.GUIDE_OPTIONS[0]: "Guide mesh",
    part_setup_pipe.GUIDE_OPTIONS[1]: "Medial curve",
}
CONTAINMENT_MAPPING = {
    part_setup_pipe.CONTAINMENT_OPTIONS[0]: "Containment body mesh",
}
USE_CASE_TO_PRINT_BASE = {
    part_setup_pipe.USE_CASE_OPTIONS[0]: "Planar",
    part_setup_pipe.USE_CASE_OPTIONS[1]: "Freeform Surface",
}


class CalculationStorage(storage.Node):
    geo_libs: params.List[mw.GeoLib] = params.List(contained_type=mw.GeoLib)
    nc_code: params.File = params.File()
    mxp_file: params.File = params.File()


@dataclass(frozen=True)
class Params:
    tool_path: params.Toolpath
    geo_libs: params.List[mw.GeoLib]
    nc_code: params.File
    mxp_file: params.File

    @staticmethod
    def create(storage: CalculationStorage) -> Params:
        if len(storage.geo_libs.val_checked) >= 1:
            tool_path = params.Toolpath(geometry.Toolpath(storage.geo_libs[-1].tool_path))
        else:
            tool_path = params.Toolpath()
        return Params(
            geo_libs=storage.geo_libs,
            tool_path=tool_path,
            nc_code=storage.nc_code,
            mxp_file=storage.mxp_file,
        )


def get_ui(
    b: ui.Builder,
    c: app.Context,
    calculation_params: Params,
    part_inputs: part_setup_pipe.Params,
    parameter_inputs: parameter_setup_pipe.Params,
    operation_storage: storage.OperationNode,
) -> list[ui.UiNode]:

    tool_path_operation_inputs = ToolPathOperationInputs(
        guide_mesh=part_inputs.guide_mesh,
        user_transform=part_inputs.user_transform,
        machining_surface=part_inputs.machining_surface,
        tube_spine=part_inputs.tube_spine,
        parts=part_inputs.containment_meshes,
        entire_machining_area=part_inputs.entire_machining_area,
        number_of_slices=part_inputs.number_of_slices,
        layer_range=parameter_inputs.layer_range,
        max_angle_between_layers=parameter_inputs.max_angle_between_layers,
        detect_layers=parameter_inputs.detect_layers_flg,
        start_point_rotation=parameter_inputs.start_point_rotation_flg,
        layer_thickness=parameter_inputs.layer_thickness,
        layer_step_over=parameter_inputs.layer_step_over,
        offset_to_curve=parameter_inputs.offset_to_curve,
        min_angle_definition=parameter_inputs.min_angle_definition,
        max_angle_definition=parameter_inputs.max_angle_definition,
        basic_tilt_angle=parameter_inputs.basic_tilt_angle,
        pattern=parameter_inputs.pattern,
    )
    part_inputs.guide.map(tool_path_operation_inputs.guide, mapping=lambda s: GUIDE_MAPPING[s])
    part_inputs.containment.map(
        tool_path_operation_inputs.containment, mapping=lambda s: CONTAINMENT_MAPPING[s]
    )
    part_inputs.use_case.map(
        tool_path_operation_inputs.print_base, mapping=lambda s: USE_CASE_TO_PRINT_BASE[s]
    )

    tool_path_operation_outputs = ToolPathOperationOutputs(
        tool_path=calculation_params.tool_path, geo_libs=calculation_params.geo_libs
    )
    tool_path_operation = ToolPathCalculation(
        tool_path_operation_inputs, tool_path_operation_outputs
    )

    posting_operation_inputs = PostingOperationInputs(
        post_definition_id=parameter_inputs.posting_definition_id,
        geo_libs=calculation_params.geo_libs,
    )
    posting_operation_outputs = PostingOperationOutputs(
        nc_code=calculation_params.nc_code, mxp_file=calculation_params.mxp_file
    )
    posting_operation = PostingOperation(c, posting_operation_inputs, posting_operation_outputs)

    combined_operation = params.Operation(
        operation.Serial([tool_path_operation, posting_operation]), storage=operation_storage
    )

    return [
        box_ui.drop_down(b, posting_operation_inputs.post_definition_id, text="Post Section"),
        box_ui.operation_control(
            b,
            combined_operation,
            text="Calculate NC Code",
            download_config=(calculation_params.nc_code, "nc-code.zip"),
        ),
        box_ui.toolpath(b, calculation_params.tool_path, name="Orig. Tool Path", visible=False),
    ]
