from __future__ import annotations

import math
from dataclasses import dataclass

import glm
import moduleworks as mw
from tcframework import box_ui
from tcframework import params
from tcframework import storage
from tcframework import ui
from tcframework import utility

from daihen_add_software.settings import MACHINE_BASE_STL

from ..utils.part import Part

GUIDE_OPTIONS = [
    "Guide curve",
    "Guide mesh",
    "Medial curve",
]
CONTAINMENT_OPTIONS = [
    "Containment body mesh",
    # "Containment curve",
    "Machining surface boundary",
]
USE_CASE_OPTIONS = ["Printing on Planar Surface", "Printing on Free-Form Surface"]

FD_B6L = "FD-B6L"


class PartStorage(storage.Node):
    use_case: params.Selection = params.Selection(
        value=USE_CASE_OPTIONS[0], options=USE_CASE_OPTIONS
    )
    machining_surface_file: params.File = params.File(allowed_extensions=[".stl"])
    machining_surface_active: params.Bool = params.Bool(False)
    # scanned_surface_file: params.File = params.File(allowed_extensions=[".stl"])
    # scanned_surface_active: params.Bool = params.Bool(False)
    # cad_surface_file: params.File = params.File(allowed_extensions=[".stl"])
    # cad_surface_active: params.Bool = params.Bool(False)
    guide: params.Selection = params.Selection(value=GUIDE_OPTIONS[2], options=GUIDE_OPTIONS)
    guide_mesh_active: params.Bool = params.Bool(False)
    guide_mesh_file: params.File = params.File(allowed_extensions=[".stl"])
    guide_curve_active: params.Bool = params.Bool(False)
    guide_curves_file: params.File = params.File(allowed_extensions=[".igs"])
    containment: params.Selection = params.Selection(
        value=CONTAINMENT_OPTIONS[0], options=CONTAINMENT_OPTIONS
    )
    # containment_curve_active: params.Bool = params.Bool(False)
    # containment_curve_file: params.File = params.File(allowed_extensions=[".igs"])
    containment_meshes_active: params.Bool = params.Bool(True)
    containment_meshes_files: params.FileList = params.FileList(allowed_extensions=[".stl"])
    entire_machining_area: params.Bool = params.Bool(value=True)
    number_of_slices: params.Int = params.Int(value=1, minimum=1)
    enable_transforms: params.Bool = params.Bool()
    rotation: params.Float = params.Float(value=0.0, minimum=-360.0, maximum=360.0)
    rotation_axis: params.Vec3 = params.Vec3(value=glm.vec3(0.0, 0.0, 1.0))
    translation: params.Vec3 = params.Vec3(value=glm.vec3(0.0, 0.0, 0.0))


@dataclass(frozen=True)
class Params:
    use_case: params.Selection
    machining_surface_file: params.File
    machining_surface_active: params.Bool
    machining_surface: params.Object[Part]
    # scanned_surface_file: params.File
    # scanned_surface_active: params.Bool
    # scanned_surface: params.Object[Part]
    # cad_surface_file: params.File
    # cad_surface_active: params.Bool
    # cad_surface: params.Object[Part]
    guide: params.Selection
    guide_mesh_active: params.Bool
    guide_mesh_file: params.File
    guide_mesh: params.Object[Part]
    guide_curves_active: params.Bool
    guide_curves_file: params.File
    guide_curves: params.MwCurveArray
    containment: params.Selection
    # containment_curves_active: params.Bool
    # containment_curves_file: params.File
    # containment_curves: params.MwCurveArray
    containment_meshes_active: params.Bool
    containment_meshes_files: params.FileList
    containment_meshes: params.List[Part]
    entire_machining_area: params.Bool
    specify_number_of_slices: params.Bool
    number_of_slices: params.Int
    enable_transforms: params.Bool
    rotation: params.Float
    rotation_axis: params.Vec3
    translation: params.Vec3
    user_transform: params.Mat4

    @staticmethod
    def create(storage: PartStorage) -> Params:
        part_parameters = Params(
            use_case=storage.use_case,
            machining_surface_file=storage.machining_surface_file,
            machining_surface_active=storage.machining_surface_active,
            machining_surface=params.Object(None),
            # scanned_surface_file=storage.scanned_surface_file,
            # scanned_surface_active=storage.scanned_surface_active,
            # scanned_surface=params.Object(None),
            # cad_surface_file=storage.cad_surface_file,
            # cad_surface_active=storage.cad_surface_active,
            # cad_surface=params.Object(None),
            guide=storage.guide,
            guide_mesh_active=storage.guide_mesh_active,
            guide_mesh_file=storage.guide_mesh_file,
            guide_mesh=params.Object(allow_none=True),
            guide_curves_active=storage.guide_curve_active,
            guide_curves_file=storage.guide_curves_file,
            guide_curves=params.MwCurveArray(allow_none=True),
            containment=storage.containment,
            # containment_curves_active=storage.containment_curve_active,
            # containment_curves_file=storage.containment_curve_file,
            # containment_curves=params.MwCurveArray(allow_none=True),
            containment_meshes_active=storage.containment_meshes_active,
            containment_meshes_files=storage.containment_meshes_files,
            containment_meshes=params.List(min_length=1, contained_type=Part),
            entire_machining_area=storage.entire_machining_area,
            specify_number_of_slices=params.Bool(),
            number_of_slices=storage.number_of_slices,
            enable_transforms=storage.enable_transforms,
            rotation=storage.rotation,
            rotation_axis=storage.rotation_axis,
            translation=storage.translation,
            user_transform=params.Mat4(),
        )

        part_parameters.entire_machining_area.map(
            part_parameters.specify_number_of_slices, lambda v: not v
        )

        # Update meshes from params.Files - only works because meshes are not changed in the template!
        update_user_transform(part_parameters)
        update_guide_mesh(part_parameters)
        update_guide_curves(part_parameters)
        # update_containment_curves(part_parameters)
        update_containment_meshes(part_parameters)
        update_machining_surface_mesh(part_parameters)
        #update_scanned_surface_mesh(part_parameters)
        #update_cad_surface_mesh(part_parameters)

        return part_parameters


def update_guide_curves(params: Params) -> None:
    if params.guide_curves_active.value and params.guide_curves_file.valid:
        _, curves, _, _, _ = mw.IGESTranslator.iges_read(
            str(params.guide_curves_file.val_checked.path)
        )
        params.guide_curves.value = curves
    else:
        params.guide_curves.value = None


def update_guide_mesh(params: Params) -> None:
    if params.guide_mesh_active.value and params.guide_mesh_file.valid:
        align_position = params.use_case.val_checked == USE_CASE_OPTIONS[0]
        params.guide_mesh.value = Part.create(params.guide_mesh_file.val_checked, align_position)
    else:
        params.guide_mesh.value = None


def update_machining_surface_mesh(params: Params) -> None:
    if params.machining_surface_active.value:
        if params.machining_surface_file.valid:
            params.machining_surface.value = Part.create(
                params.machining_surface_file.val_checked,
                False,
            )
    else:
        params.machining_surface.value = Part.create(utility.File(MACHINE_BASE_STL), False)

# def update_scanned_surface_mesh(params: Params) -> None:
#     if params.scanned_surface_active.value:
#         if params.scanned_surface_file.valid:
#             params.scanned_surface.value = Part.create(
#                 params.scanned_surface_file.val_checked,
#                 False,
#             )
#     else:
#         params.scanned_surface.value = Part.create(utility.File(SCANNED_BASE_STL), False)

# def update_cad_surface_mesh(params: Params) -> None:
#     if params.cad_surface_active.value:
#         if params.cad_surface_file.valid:
#             params.cad_surface.value = Part.create(
#                 params.cad_surface_file.val_checked,
#                 False,
#             )
#     else:
#         params.cad_surface.value = Part.create(utility.File(CAD_BASE_STL), False)


# def update_containment_curves(params: Params) -> None:
#    if params.containment_curves_active.value and params.containment_curves_file.valid:
#        _, curves, _, _, _ = mw.IGESTranslator.iges_read(
#            str(params.containment_curves_file.val_checked.path)
#        )
#        params.containment_curves.value = MwCurveService.transform_curve_array(
#            curves, params.user_transform.val_checked
#        )
#    else:
#        params.containment_curves.value = None


def update_containment_meshes(params: Params) -> None:
    if params.containment_meshes_active.value and params.containment_meshes_files.valid:
        align_position = params.use_case.val_checked == USE_CASE_OPTIONS[0]
        params.containment_meshes.value = [
            Part.create(file, align_position)
            for file in params.containment_meshes_files.val_checked
        ]
    else:
        params.containment_meshes.value = []


def update_user_transform(params: Params) -> None:
    if (
        params.enable_transforms.value
        and params.rotation.value is not None
        and params.translation.value is not None
        and params.rotation_axis.value is not None
    ):
        rotation: glm.mat4 = glm.rotate(
            math.radians(params.rotation.val_checked), params.rotation_axis.val_checked
        )
        params.user_transform.value = rotation * glm.translate(params.translation.val_checked)
    else:
        params.user_transform.value = glm.mat4()


def get_ui(b: ui.Builder, params: Params) -> list[ui.UiNode]:
    params.use_case.map(params.machining_surface_active, mapping=lambda x: x == USE_CASE_OPTIONS[1])
    #params.use_case.map(params.scanned_surface_active, mapping=lambda x: x == USE_CASE_OPTIONS[1])
    #params.use_case.map(params.cad_surface_active, mapping=lambda x: x == USE_CASE_OPTIONS[1])
    params.guide.map(params.guide_mesh_active, mapping=lambda x: x == GUIDE_OPTIONS[1])
    params.guide.map(params.guide_curves_active, mapping=lambda x: x == GUIDE_OPTIONS[0])

    params.guide.on_change(lambda _: update_guide_mesh(params))
    params.guide_mesh_file.on_change(lambda _: update_guide_mesh(params))
    params.guide_curves_file.on_change(lambda _: update_guide_curves(params))

    params.machining_surface_file.on_change(lambda _: update_machining_surface_mesh(params))
    params.use_case.on_change(lambda _: update_machining_surface_mesh(params))

    #params.scanned_surface_file.on_change(lambda _: update_scanned_surface_mesh(params))
    #params.use_case.on_change(lambda _: update_scanned_surface_mesh(params))

    #params.cad_surface_file.on_change(lambda _: update_cad_surface_mesh(params))
    #params.use_case.on_change(lambda _: update_cad_surface_mesh(params))

    # params.containment_curves_file.on_value_change(
    #    if_valid=lambda _: update_containment_curves(params)
    # )
    params.containment_meshes_files.on_change(lambda _: update_containment_meshes(params))
    params.use_case.on_change(
        lambda _: update_containment_meshes(params), skip_initial_evaluation=True
    )

    params.containment.map(
        params.containment_meshes_active, mapping=lambda x: x == CONTAINMENT_OPTIONS[0]
    )
    # params.containment.map(
    #    params.containment_curves_active, mapping=lambda x: x == CONTAINMENT_OPTIONS[1]
    # )

    params.enable_transforms.on_change(lambda _: update_user_transform(params))
    params.rotation.on_change(lambda _: update_user_transform(params))
    params.rotation_axis.on_change(lambda _: update_user_transform(params))
    params.translation.on_change(lambda _: update_user_transform(params))

    return [
        box_ui.drop_down(b, params.use_case, "Use Case", wide=True),
        ui.active_if(
            b,
            params.machining_surface_active,
            box_ui.file_upload(b, params.machining_surface_file, "Machining Surface"),
        ),
        box_ui.drop_down(b, params.guide, text="Pattern", wide=True),
        ui.active_if(
            b,
            params.guide_mesh_active,
            box_ui.file_upload(b, params.guide_mesh_file, text="Guide mesh"),
        ),
        ui.active_if(
            b,
            params.guide_curves_active,
            box_ui.file_upload(b, params.guide_curves_file, text="Guide curve"),
        ),
        box_ui.drop_down(b, params.containment, text="Containment", wide=True),
        # ui.active_if(
        #     b,
        #     params.containment_curves_active,
        #     box_ui.file_upload(b, params.containment_curves_file, text="Containment curve(s)"),
        # ),
        ui.active_if(
            b,
            params.containment_meshes_active,
            box_ui.multi_file_upload(
                b, params.containment_meshes_files, text="Containment mesh(es)"
            ),
        ),
        #box_ui.file_upload(b, params.scanned_surface_file, "Scanned Surface Geometry"),
        #box_ui.file_upload(b, params.cad_surface_file, "CAD Surface Geometry"),
        box_ui.checkbox(b, params.entire_machining_area, text="Entire machining area"),
        ui.active_if(
            b,
            params.specify_number_of_slices,
            box_ui.number_field(b, params.number_of_slices, text="Number of slices"),
        ),
        box_ui.checkbox(b, params.enable_transforms, text="Rotation/Translational Shifts"),
        ui.active_if(
            b,
            params.enable_transforms,
            box_ui.number_field(
                b, params.rotation, text="Rotation Angle Value", step=15.0, extra_text="Deg"
            ),
        ),
        ui.active_if(
            b,
            params.enable_transforms,
            box_ui.vector(b, params.rotation_axis, text="Rotation Axis"),
        ),
        ui.active_if(
            b,
            params.enable_transforms,
            box_ui.vector(b, params.translation, text="Translational Shifts", extra_text="mm"),
        ),
    ]
