# (C) 2024 ModuleWorks GmbH
# Owner: Industry Project
import os
import shutil
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path

import moduleworks as mw
from tcframework import app
from tcframework import error
from tcframework import operation
from tcframework import params
from tcframework import utility
from tcframework.utility import machine_adapter

from daihen_add_software import settings


class PPFProgressHandler(mw.ppinterface.ProgressHandler):
    def __init__(
        self, operation_context: operation.ExecutionContext, total_number_of_moves: int
    ) -> None:
        super().__init__()
        self.context = operation_context
        self.total_number_of_moves = total_number_of_moves

    def update_progress(self, progress_tree: dict[str, int]) -> None:
        handled_number_of_moves = sum(progress_tree.values())
        if self.total_number_of_moves == 0:
            self.context.progress = 100
        else:
            self.context.progress = 100 * (handled_number_of_moves / self.total_number_of_moves)


class CancelHandler(mw.ppinterface.CancelHandler):
    def __init__(self, operation_context: operation.ExecutionContext) -> None:
        super().__init__()
        self.context = operation_context

    def should_cancel(self) -> bool:
        return self.context.abort


@dataclass(frozen=True)
class PostingOperationInputs:
    post_definition_id: params.Selection[str]
    geo_libs: params.List[mw.GeoLib] = field(
        default_factory=lambda: params.List(min_length=1, contained_type=mw.GeoLib)
    )


@dataclass(frozen=True)
class PostingOperationOutputs:
    nc_code: params.File = field(default_factory=params.File)
    mxp_file: params.File = field(default_factory=params.File)


class PostingOperation(operation.ABC[PostingOperationInputs, PostingOperationOutputs]):
    def __init__(
        self, c: app.Context, inputs: PostingOperationInputs, outputs: PostingOperationOutputs
    ) -> None:
        super().__init__(inputs, outputs)
        self.c = c
        machine_xml = settings.MACHINE_XML
        self.machine_definition = machine_adapter.MachineDefinition(
            machine_xml,
            failing_mode=machine_adapter.GeometriesFailingMode.IGNORE_MISSING,
        )
        self.post_definitions_ids = self.machine_definition.post_definition_ids
        # First update the options
        self.inputs.post_definition_id.update_options(
            options=self.machine_definition.post_definition_ids,
            option_name=lambda option: option.replace("_", " "),
        )
        # Then set Free_Spin_In_Z as the default selection
        default_post_id = "Free_Spin_In_Z" if "Free_Spin_In_Z" in self.machine_definition.post_definition_ids else None
        self.inputs.post_definition_id.value = default_post_id

    async def run(self, context: operation.ExecutionContext) -> None:
        self.outputs.nc_code.value = None

        post_definition = self.machine_definition.all_post_definitions.get_post_definition(
            self.inputs.post_definition_id.val_checked
        )
        machsim_mach_def = self.machine_definition.get_machsim_mach_def()
        root_node = mw.ppinterface.OperationGroup("root", "0")
        root_node.machine_settings = mw.mw_pp_converter.machine_settings_factory(machsim_mach_def)

        geo_lib = self.inputs.geo_libs.val_checked[-1]
        tool_info = geo_lib.tool_info
        #post_definition.mxp_param.un

        tool_length = tool_info.get_tool_part_length(mw.cadcam.Tool.ToolPartFlags.TPF_ALL)
        tp_creator = mw.post.PostedNaxTPCreator(
            machsim_mach_def,
            post_definition.mxp_param,
            post_definition.mach_dynamics,
            post_definition.workpiece_name,
            post_definition.tool_name,
            15.00,
            0.001,
        )

        try:
            tp_creator.post_run(geo_lib.tool_path)
        except mw.misc.MwException as mwE:
            raise error.UserError(str(mwE))
        except Exception as e:
            raise e
        finally:
            operation = mw.mw_pp_converter.operation_factory(
                tp_creator, geo_lib, CancelHandler(context), machsim_mach_def
            )
            operation.name = f"WAAM"
            operation.id = f"WAAM"

            root_node.add_child(operation)

            def init_mxp_output(mxp_file: Path) -> None:
                mw.MXPOutput(
                    str(mxp_file),
                    tp_creator,
                    self.inputs.geo_libs.val_checked[-1].tool_path,
                )

        self.outputs.mxp_file.value = self.c.file_system.create_and_init(init_mxp_output)
        pp_additional_data = mw.ppinterface.AdditionalData()
        cam_info = mw.ppinterface.CAMInformation(
            "WAAM Workflow",
            "1.0",
            __file__,
            str(self.machine_definition.machine_path),
        )
        ppf_input = mw.ppinterface.PPFrameworkInput(root_node, cam_info, pp_additional_data)

        posted_nax_tp_creator = mw.PostedTPCreatorCaster.safe_cast_Nax(tp_creator)  # type: ignore[arg-type]
        machine = mw.mw_pp_converter.machine_factory(machsim_mach_def, posted_nax_tp_creator)

        ppf_input.add_machine_uniquely(machine)

        mw.ppinterface.post_on_server(
            ppf_input,
            settings.POSTING_PORT,
            "127.0.0.1",
            PPFProgressHandler(context, operation.move_list_size),
        )

        output_dir = Path(settings.OUTPUT_DIR)

        directories = [
            output_dir / f
            for f in os.listdir(output_dir)
            if os.path.isdir(output_dir / f) and "nc_" in f
        ]
        most_recent_directory = max(directories)

        def init_nc_code_file(path: Path) -> None:
            shutil.make_archive(path.as_posix(), "zip", most_recent_directory)

        self.outputs.nc_code.value = self.c.file_system.create_and_init(init_nc_code_file)
        nc_code_file = utility.File(Path(self.outputs.nc_code.value.path.as_posix() + ".zip"))
        self.outputs.nc_code.value = nc_code_file
