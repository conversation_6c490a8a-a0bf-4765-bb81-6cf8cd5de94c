;--------------------------------
;Include Modern UI

  !include "MUI2.nsh"
  !include "nsDialogs.nsh"
  !include "LogicLib.nsh"
  !include "FileFunc.nsh"
  
;--------------------------------
;General

  ;Name and file
  !ifndef APP_NAME
  !define APP_NAME "Daihen.ADD.software"
  !endif

  Name "${APP_NAME}"
  !define /date MY_TIMESTAMP "%Y-%m-%d_%H.%M"

  ; Resolve Git revision and branch
  ; Unless they are provided in command line
  !tempfile STDOUT_HOLDER
  !echo "Stdout holder: ${STDOUT_HOLDER}"
  !ifndef APP_VERSION
  !system '"git" log -1 --pretty=format:"%h" > "${STDOUT_HOLDER}"'
  !define /file APP_VERSION "${STDOUT_HOLDER}"
  !endif

  !searchreplace VERSION_FILTERED ${APP_VERSION} "/" "-"
 
  OutFile "${APP_NAME}_${MY_TIMESTAMP}_${VERSION_FILTERED}.exe"
  Unicode True
  RequestExecutionLevel highest ;Require user rights on NT6+ (When UAC is turned on)

  ;Default installation folder
  InstallDir "$LOCALAPPDATA\Programs\${APP_NAME}"

  ;Get installation folder from registry if available
  InstallDirRegKey HKCU "Software\${APP_NAME}" ""
  
  !ifndef EXE_NAME
  !define EXE_NAME "${APP_NAME}"
  !endif

  !define EXE_LOC "$instdir\${EXE_NAME}.exe"
  !define DIST_FOLDER "dist\app"
  !define MUI_ICON "${DIST_FOLDER}\\assets\\static\\daihen_icon.ico"
  !define MUI_UNICON "${DIST_FOLDER}\\assets\\static\\daihen_icon.ico" 

Function finishpageaction
      CreateShortcut "$desktop\${EXE_NAME}.lnk" "$INSTDIR\Python\pythonw.exe" "$INSTDIR\app.pyw" "$INSTDIR\assets\static\daihen_icon.ico" 0
FunctionEnd


;--------------------------------
;Interface Settings

  !define MUI_ABORTWARNING

;--------------------------------
;Pages
  !define MUI_FINISHPAGE_SHOWREADME ""
  !define MUI_FINISHPAGE_SHOWREADME_TEXT "Create Desktop Shortcut"
  !define MUI_FINISHPAGE_SHOWREADME_FUNCTION finishpageaction
	
  !insertmacro MUI_PAGE_WELCOME
  !insertmacro MUI_PAGE_INSTFILES
  !insertmacro MUI_PAGE_FINISH

  !insertmacro MUI_UNPAGE_WELCOME
  !insertmacro MUI_UNPAGE_CONFIRM
  !insertmacro MUI_UNPAGE_INSTFILES
  !insertmacro MUI_UNPAGE_FINISH


;--------------------------------
;Languages

  !insertmacro MUI_LANGUAGE "English"

ShowInstDetails show

Function .onInit
  SetRegView 64
FunctionEnd

Function un.onInit
  SetRegView 64
FunctionEnd

Function .onInstSuccess
  ;Store installation folder
  WriteRegStr HKCU "Software\\${APP_NAME}" "" $INSTDIR

  ;Create uninstaller
  WriteUninstaller "$INSTDIR\\Uninstall.exe"
  
  WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" \
                 "DisplayName" "${APP_NAME}"
  WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" \
                 "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
				 
  WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" \
                 "DisplayVersion" "${APP_VERSION}"
				 
  WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" \
                 "DisplayIcon" "$INSTDIR\assets\static\daihen_icon.ico"
FunctionEnd


Section "${APP_NAME}" SecApp
  SetOutPath $INSTDIR

  File /r "${DIST_FOLDER}\\*"

  CreateShortcut "${EXE_NAME}.exe.lnk" "$INSTDIR\Python\pythonw.exe" "$INSTDIR\app.pyw" "$INSTDIR\assets\settings\daihen_icon.ico" 0
  CreateDirectory "$SMPROGRAMS\${EXE_NAME}"
  CreateShortcut "$SMPROGRAMS\${EXE_NAME}\${EXE_NAME}.lnk" "$INSTDIR\Python\pythonw.exe" "$INSTDIR\app.pyw" "$INSTDIR\assets\static\daihen_icon.ico" 0

  ;Store installation folder
  WriteRegStr HKCU "Software\\${APP_NAME}" "" $INSTDIR

  ;Create uninstaller
  WriteUninstaller "$INSTDIR\\Uninstall.exe"
SectionEnd

Section "Uninstall"
  RMDir /r $APPDATA\tcelectron
  StrCpy $1 "\\?\$LOCALAPPDATA\template_cam\inst__daihen_add_software\tmp"
  ;StrCpy $2 "\\?\$LOCALAPPDATA\template_cam\inst__daihen_add_software\tc_logs"
  
  DeleteRegKey HKCU "Software\${APP_NAME}"
  DeleteRegKey HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
  Delete "$desktop\${APP_NAME}.lnk"
  
  Delete $INSTDIR\Uninstall.exe
  RMDir /r $INSTDIR
  RMDir /r "$SMPROGRAMS\${EXE_NAME}"

  RMDir /r $0
  RMDir /r $1
  RMDir /r $2
SectionEnd

; !finalize './sign.sh "%1"' = 0
; !uninstfinalize './sign.sh "%1"' = 0
