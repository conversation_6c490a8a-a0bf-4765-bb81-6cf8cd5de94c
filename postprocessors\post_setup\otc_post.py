from ppframework.pplib.post_processor import MoveWindowPostProcessor
from ppframework.core.data_model.machine.axis import TranslationalAxis
from ppframework.pplib.nc_generation.writer import NCCodeFileWriter
from .otc_nc_generator import OTCNCGenerator
from ppframework.settings import Settings, Attribute
from ppframework.core.types import EulerAngleType
import csv
import os


class OTCSettings(Settings):
    EULER_ANGLE_TYPE = Attribute(default=EulerAngleType.RzRyRx)
    WIRE_FEED_SPEED = Attribute(default=500, type=int)
    WELDING_SPEED = Attribute(default=70, type=int)
    ROBOT_SPEED = Attribute(default=38.0, type=float)
    ROTARY_TABLE_SPEED = Attribute(default=100.0, type=float)
    IS_JOINT_OUTPUT = Attribute(default=False, type=bool)
    NC_TOLERANCE_DECIMAL_PLACES_POSITIONER = Attribute(default=2, type=int)
    MAXIMUM_FILE_SIZE = Attribute(default=999, type=int)
    NC_HAS_SEQUENCE_NUMBERING = True
    NC_TOLERANCE_DECIMAL_PLACES = 2
    NC_HAS_ONE_PROGRAM_PER_OPERATION = False
    IS_STATIC_OUTPUT = Attribute(default=False, type=bool)
    IS_WELDING_ON = Attribute(default=True, type=bool)
    Current = Attribute(default=115, type=int)


class OTCPostProcessor(MoveWindowPostProcessor):
    nc_generator: OTCNCGenerator
    settings: OTCSettings

    def __init__(self, settings, nc_gen, writer):
        super().__init__(settings=settings, nc_generator=nc_gen, writer=writer, sight_distance=2)
        #self.writer2 = writer2
        #self.nc_generator2=nc_gen2
        self.program_index = 3

        # Initialize CSV file for joint values
        self.csv_file = None
        self.csv_writer = None
        self.move_counter = 0

    def _configure_machine(self, machine):
        # add virtual axis for part mode (robot has only joints but part positions need an axis internally)
        x_axis = TranslationalAxis(settings=self.settings, uid='X')
        y_axis = TranslationalAxis(settings=self.settings, uid='Y')
        z_axis = TranslationalAxis(settings=self.settings, uid='Z')

        machine.axes.append(x_axis)
        machine.axes.append(y_axis)
        machine.axes.append(z_axis)

        for controller in machine.controllers:
            for channel in controller.channels:
                channel.axes.append(x_axis)
                channel.axes.append(y_axis)
                channel.axes.append(z_axis)

        return machine

    def _initialize_csv_file(self):
        """Initialize CSV file for joint values output"""
        try:
            # Create CSV file in the same directory as NC output
            output_dir = getattr(self.settings, 'NC_OUTPUT_DIR', '.')
            csv_filename = os.path.join(output_dir, 'robot_joint_values.csv')

            self.csv_file = open(csv_filename, 'w', newline='', encoding='utf-8')
            self.csv_writer = csv.writer(self.csv_file)

            # Write CSV header with correct joint mapping
            header = [
                'Move_Index',
                'Joint_1_R1', 'Joint_2_R2', 'Joint_3_R3',
                'Joint_4_R4', 'Joint_5_R5', 'Joint_6_R6',
                'Positioner_A', 'Positioner_C',
                'X_Position', 'Y_Position', 'Z_Position'
            ]
            self.csv_writer.writerow(header)
            print(f"📊 CSV file created: {csv_filename}")

        except Exception as e:
            print(f"⚠️ Error creating CSV file: {e}")
            self.csv_file = None
            self.csv_writer = None

    def _write_joint_values_to_csv(self, move, move_index):
        """Write joint values and position data to CSV file"""
        if self.csv_writer is None:
            return

        try:
            # Extract joint values from the move using correct mapping
            # Robot joints start from index 2 (JointR1 = index 2, JointR2 = index 3, etc.)
            joint_values = []

            # Get robot joint values (6 joints) - correct indices 2-7
            robot_joint_indices = [2, 3, 4, 5, 6, 7]  # JointR1 through JointR6
            for joint_idx in robot_joint_indices:
                if joint_idx < len(move.rotation_axis_values):
                    joint_values.append(f"{move.rotation_axis_values[joint_idx]:.3f}")
                else:
                    joint_values.append("0.000")

            # Get positioner values (A and C axes are at indices 0 and 1)
            positioner_a = "0.000"  # A axis (index 0)
            positioner_c = "0.000"  # C axis (index 1)

            if len(move.rotation_axis_values) > 0:
                positioner_a = f"{move.rotation_axis_values[0]:.3f}"  # A axis
            if len(move.rotation_axis_values) > 1:
                positioner_c = f"{move.rotation_axis_values[1]:.3f}"  # C axis

            # Get position values
            x_pos = f"{move.part_position.x:.3f}" if hasattr(move, 'part_position') else "0.000"
            y_pos = f"{move.part_position.y:.3f}" if hasattr(move, 'part_position') else "0.000"
            z_pos = f"{move.part_position.z:.3f}" if hasattr(move, 'part_position') else "0.000"

            # Write row to CSV with correct joint mapping
            row = [
                move_index,
                joint_values[0], joint_values[1], joint_values[2],  # Robot joints R1, R2, R3
                joint_values[3], joint_values[4], joint_values[5],  # Robot joints R4, R5, R6
                positioner_a, positioner_c,  # Positioner A and C axes
                x_pos, y_pos, z_pos
            ]

            self.csv_writer.writerow(row)
            self.move_counter += 1

        except Exception as e:
            print(f"⚠️ Error writing joint values to CSV: {e}")

    def _process_operation_group_prologue(self, operation_group, index, *args, **kwargs):
        self.settings.NC_DEFAULT_FILE_NAME = f"UNIT2-A.{2:03d}"
        self.writer <<= self.nc_generator.StartOfProgramBlock()
        self.writer << f"CALLP {self.program_index}"

        # Create CSV file for joint values
        self._initialize_csv_file()

    def _process_operation_prologue(self, operation, index, operation_group, *args, **kwargs):
        self.settings.NC_DEFAULT_FILE_NAME = f"UNIT2-A.{self.program_index:03d}"
        self.writer <<= self.nc_generator.CallingOfSubprogramBlock(sub_program_name=self.settings.NC_DEFAULT_FILE_NAME)
        self.nc_generator.controller.part_mode_on()
        #self.writer2 <<= self.nc_generator2.StartOfProgramBlock("Points.txt")

    def _process_move(self, move_window, index, operation, operation_group, *args, **kwargs):
        previous_move = move_window.previous_move
        move = move_window.current_move
        next_move = move_window.next_move
        #self.writer2 << f"{move.part_position.x},{move.part_position.y},{move.part_position.z}"
        size = self.nc_generator.nc_program_manager.current_program.sequence_number_state_model.value or 0

        # Write joint values to CSV
        self._write_joint_values_to_csv(move, index)

        if size > self.settings.MAXIMUM_FILE_SIZE - int(0.05 * self.settings.MAXIMUM_FILE_SIZE):
            self.writer << "END"
            self.writer <<= self.nc_generator.EndOfSubprogramBlock()
            self.program_index += 1
            self.writer << f"CALLP {self.program_index}"
            self.writer <<= self.nc_generator.CallingOfSubprogramBlock(sub_program_name=f"UNIT2-A.{self.program_index:03d}")

        if self.settings.IS_STATIC_OUTPUT:
            self.fixed_coordinate_system(operation, move, index)
        else:
            if index == 0:
                self.writer <<= self.nc_generator.JointInterpolationBlock(move=move)
            else:
                self.writer <<= self.nc_generator.LinearInterpolationBlock(move=move)
        if self.settings.IS_WELDING_ON:
            if move.marker is not None:
                if move.marker.is_layer_start or move.marker.is_slice_start:
                    #if move.marker.layer_index > 1:
                        #self.writer << f"ASWBPL 1,0,0,3,1,0,1,2,0,0,50,0,0,0,1,{operation.layers[move.marker.layer_index].process_parameters.WeldingCurrent},150,2,0,{35},0,0,0,0,0,0,0,0,0,0,50,0,0,0,257,0,0,0,150,150,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,50,0,0,0,0,32,0,80,0"
                    #else:
                        #self.writer << f"ASWBPL 1,0,0,3,1,0,1,2,0,0,50,0,0,0,1,{operation.layers[move.marker.layer_index].process_parameters.WeldingCurrent},150,2,0,{35},0,0,0,0,0,0,0,0,0,0,50,0,0,0,257,0,0,0,150,150,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,50,0,0,0,0,32,0,80,0"
                    self.writer << f"CALLP 6002"
                elif move.marker.is_layer_end or move.marker.is_slice_end:
                    #self.writer << f"AEWBPL 1,0,2,1,0,1,2,0,0,{90},2,0,0,0,0,0,0,0,0,10,50,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,100,0,3,0,80,0,32,0,0,0,0,0,0,0,0,0,0,0,0"
                    #self.writer << f"DELAY 60"#{operation.layers[move.marker.layer_index].process_parameters.Delay}"
                    self.writer << f"CALLP 6003"

    def _process_operation_epilogue(self, operation, operation_index, operation_group, *args, **kwargs):
        self.writer << "END"
        self.writer <<= self.nc_generator.EndOfSubprogramBlock()

    def _process_operation_group_epilogue(self, operation_group, index, parent = ..., *args, **kwargs):
        self.writer <<= self.nc_generator.EndOfProgramBlock()

        # Close CSV file
        self._close_csv_file()

    def _close_csv_file(self):
        """Close the CSV file and print summary"""
        if self.csv_file:
            try:
                self.csv_file.close()
                print(f"✅ CSV file closed. Total moves written: {self.move_counter}")
            except Exception as e:
                print(f"⚠️ Error closing CSV file: {e}")
            finally:
                self.csv_file = None
                self.csv_writer = None

    def fixed_coordinate_system(self, operation, move, index):
        import numpy
        from ppframework.core.ppf_math import Quaternion
        q_w = operation.additional_data._data['named_frames'][index]._data['workpiece_transform1'].orientation
        workpiece_position = operation.additional_data._data['named_frames'][index]._data['workpiece_transform1'].origin
        wh_x = workpiece_position.x
        wh_y = workpiece_position.y
        wh_z = workpiece_position.z
        m_w_rot = Quaternion(q_w.q0, q_w.q1, q_w.q2, q_w.q3).as_rotation_matrix()
        m_w_as_numpy_matrix = numpy.matrix(
            [[m_w_rot.r11, m_w_rot.r12, m_w_rot.r13, wh_x],
             [m_w_rot.r21, m_w_rot.r22, m_w_rot.r23, wh_y],
             [m_w_rot.r31, m_w_rot.r32, m_w_rot.r33, wh_z],
             [0, 0, 0, 1]])

        q_t = operation.additional_data._data['named_frames'][index]._data['holder_transform'].orientation
        tool_position = operation.additional_data._data['named_frames'][index]._data['holder_transform'].origin
        wt_x = tool_position.x
        wt_y = tool_position.y
        wt_z = tool_position.z
        m_t_rot = Quaternion(q_t.q0, q_t.q1, q_t.q2, q_t.q3).as_rotation_matrix()
        m_t_as_numpy_matrix = numpy.matrix(
            [[m_t_rot.r11, m_t_rot.r12, m_t_rot.r13, wt_x],
            [m_t_rot.r21, m_t_rot.r22, m_t_rot.r23, wt_y],
            [m_t_rot.r31, m_t_rot.r32, m_t_rot.r33, wt_z],
            [0, 0, 0, 1]])
        #self.writer << f"Tool position: {tool_position}"

        resulting_matrix = numpy.dot(numpy.linalg.inv(m_w_as_numpy_matrix), m_t_as_numpy_matrix)
        #m_t_inv = numpy.linalg.inv(m_t_as_numpy_matrix)
        m_w_inv = numpy.linalg.inv(m_w_as_numpy_matrix)
        #resulting_matrix = numpy.matmul(m_w_inv, m_t_as_numpy_matrix )
        #euler = Quaternion.as_euler_angles(resulting_quat, euler_angle_type=EulerAngleType.RxRyRz)
        resulting_q = self.quaternion_division(q_w, q_t)
        euler = resulting_q.as_euler_angles(euler_angle_type=EulerAngleType.RzRyRx)
        #rot_matrix = RotationMatrix(resulting_matrix[0,0],resulting_matrix[1,0],resulting_matrix[2,0],
                                   #resulting_matrix[0,1],resulting_matrix[1,1],resulting_matrix[2,1],
                                   #resulting_matrix[0,2],resulting_matrix[1,2],resulting_matrix[2,2])
        #euler = rot_matrix.as_euler_angles(euler_angle_type=EulerAngleType.RzRyRx)

        part_position_x = resulting_matrix[0, 3]
        part_position_y = resulting_matrix[1, 3]
        part_position_z = resulting_matrix[2, 3]
        #self.writer <<= self.nc_generator.LinearInterpolationBlock(move=move, *args, **kwargs)
        self.writer << (f"MOVEX A=1,AC=0,SM=0,HM,M1X,L,({part_position_x:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f},"
                        f"{part_position_y:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f},"
                        f"{part_position_z:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f},"
                        f"{euler[0]:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f},"
                        f"{euler[1]:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f},"
                        f"{euler[2]:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f}),"
                        f" S = 38.0, H = 1, MS, M2J, P,"
                        f" ({move.rotation_axis_values[6]:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f},"
                        f"{move.rotation_axis_values[7]:.{self.settings.NC_TOLERANCE_DECIMAL_PLACES}f}),S= 100.0,H=1")

    def quaternion_division(self, q_w, q_t):
        import math
        from ppframework.core.ppf_math import Quaternion
        division = q_w.q0 * q_w.q0 + q_w.q1 * q_w.q1 + q_w.q2 * q_w.q2 + q_w.q3 * q_w.q3
        resulting_q0 = (q_w.q0 * q_t.q0 + q_w.q1 * q_t.q1 + q_w.q2 * q_t.q2 + q_w.q3 * q_t.q3) / division
        resulting_q1 = (q_w.q0 * q_t.q1 - q_w.q1 * q_t.q0 - q_w.q2 * q_t.q3 + q_w.q3 * q_t.q2) / division
        resulting_q2 = (q_w.q0 * q_t.q2 + q_w.q1 * q_t.q3 - q_w.q2 * q_t.q0 - q_w.q3 - q_t.q1) / division
        resulting_q3 = (q_w.q0 * q_t.q3 - q_w.q1 * q_t.q2 + q_w.q2 * q_t.q1 - q_w.q3 * q_t.q0) / division
        length = math.sqrt(
            resulting_q0 * resulting_q0 + resulting_q1 * resulting_q1 + resulting_q2 * resulting_q2 + resulting_q3 * resulting_q3)
        resulting_q0 = resulting_q0 / length
        resulting_q1 = resulting_q1 / length
        resulting_q2 = resulting_q2 / length
        resulting_q3 = resulting_q3 / length
        return Quaternion(resulting_q0, resulting_q1, resulting_q2, resulting_q3)


def main():
    settings = OTCSettings()
    nc_gen = OTCNCGenerator(settings=settings)
    #nc_gen2 = OTCNCGenerator(settings=settings)
    writer = NCCodeFileWriter(settings, nc_generator=nc_gen)
    #writer2 = NCCodeFileWriter(settings, nc_generator=nc_gen2)
    OTCPostProcessor(settings, nc_gen, writer).run()