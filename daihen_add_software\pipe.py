# (C) 2024 ModuleWorks GmbH

from tcframework import app
from tcframework import box_ui
from tcframework import layout
from tcframework import ui

from .storage_pipe import Storage
from .ui_blocks import calculation_pipe
from .ui_blocks import parameter_setup_pipe
from .ui_blocks import part_setup_pipe
from .ui_blocks import simulation_pipe


@app.template(name="Multi-Axis WAAM Pipe", tags=["Multi-Axis WAAM Pipe", "Daihen"])
def pipe(b: ui.Builder, c: app.Context) -> ui.UiNode:
    storage = c.get_storage(Storage)
    user_params = parameter_setup_pipe.Params.create(storage.parameter_storage)
    part_parameters = part_setup_pipe.Params.create(storage.part_storage)
    calculation_params = calculation_pipe.Params.create(storage.calculation_storage)
    part_setup_elements = part_setup_pipe.get_ui(b, part_parameters)
    parameter_setup_elements = parameter_setup_pipe.get_ui(b, user_params)

    calculation_elements = calculation_pipe.get_ui(
        b, c, calculation_params, part_parameters, user_params, storage.operation
    )
    simulation_elements, simulation_handler = simulation_pipe.get_ui(
        b, part_parameters, calculation_params
    )



    axes_widget = ui.axes_widget()
    axes_widget_box = ui.column(
        [axes_widget],
        padding=layout.OUTER_SPACING,
        align=ui.Align.START,
    )

    left = ui.column(
        [
            ui.column(
                [
                    box_ui.widget_box(b, elements=part_setup_elements, title="Part Setup"),
                    box_ui.widget_box(
                        b, elements=parameter_setup_elements, title="Parameter Setup"
                    ),
                    box_ui.widget_box(
                        b, elements=calculation_elements, title="NC Code Calculation", height="13em"
                    ),
                    box_ui.widget_box(b, elements=simulation_elements, title="Simulation"),
                ],
                gap=layout.OUTER_SPACING,
                padding=layout.OUTER_SPACING,
                grow=True,
            ),
            axes_widget_box,
        ]
    )

    right = layout.spaced_column([layout.object_tree_and_axes()])
    panes = layout.panes_left_right(left, right)
    return layout.geometry_viewer_overlay(
        panes,
        viewer=ui.geometry_viewer(b, simulation_handler.initial_view),
    )
