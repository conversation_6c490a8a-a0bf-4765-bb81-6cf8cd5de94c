from __future__ import annotations

from dataclasses import dataclass
import json
from pathlib import Path

from tcframework import box_ui
from tcframework import params
from tcframework import storage
from tcframework import ui

PATTERN_OPTIONS = ["ZigZag", "OneWay", "Spiral"]

MATERIALS_FILE = Path(__file__).parent.parent.parent / "assets" / "reference_files" / "materials.json"

def load_materials():
    with open(MATERIALS_FILE, "r") as f:
        data = json.load(f)
    return data["materials"]

MATERIALS = load_materials()
MATERIAL_OPTIONS = [m["name"] for m in MATERIALS]
MATERIAL_TO_PROPS = {m["name"]: m for m in MATERIALS}

# Shared state for sync between tabs
if not hasattr(ui, "_welding_sync_state"):
    # Initialize with first material's values
    first_material = MATERIAL_OPTIONS[0]
    first_props = MATERIAL_TO_PROPS[first_material]

    ui._welding_sync_state = {
        "material": params.Selection(value=first_material, options=MATERIAL_OPTIONS),
        "welding_params": {},
        "version": params.Int(value=0),
        # Single set of parameter objects that will be updated when material changes
        "current_welding_current": params.Int(value=first_props["welding_parameters"]["current_set"]),
        "current_welding_voltage": params.Int(value=first_props["welding_parameters"]["voltage_set"]),
        "current_welding_speed": params.Int(value=first_props["welding_parameters"]["travel_speed"]),
    }
state = ui._welding_sync_state

# Initialize welding params for all materials
for mat in MATERIAL_OPTIONS:
    if mat not in state["welding_params"]:
        props = MATERIAL_TO_PROPS[mat]
        state["welding_params"][mat] = {
            "welding_current": params.Int(value=props["welding_parameters"]["current_set"]),
            "welding_voltage": params.Int(value=props["welding_parameters"]["voltage_set"]),
            "welding_speed": params.Int(value=props["welding_parameters"]["travel_speed"]),
        }


class ParameterStorage(storage.Node):
    json_file: params.File = params.File(allowed_extensions=[".json"])

    layer_range: params.IntRange = params.IntRange(value=(1, 1), minimum=1, maximum=1000)

    layer_selection_active_flg: params.Bool = params.Bool(value=True)

    detect_layers_flg: params.Bool = params.Bool(value=False)

    layer_thickness: params.Float = params.Float(value=3.0, minimum=1e-12)

    layer_step_over: params.Float = params.Float(value=5.0, minimum=1e-12)

    offset_to_curve: params.Float = params.Float(value=-5.0, minimum=-100.00)

    min_angle_definition: params.Float = params.Float(value=0.0, minimum=-1e-12)

    max_angle_definition: params.Float = params.Float(value=90.0, minimum=1e-12)

    basic_tilt_angle: params.Float = params.Float(value=15.0, minimum=-360.00)

    start_point_rotation_flg: params.Bool = params.Bool(value=False)

    alternate_direction_for_each_layer_flg: params.Bool = params.Bool(value=False)

    pattern: params.Selection = params.Selection(value="ZigZag", options=PATTERN_OPTIONS)

    posting_definition_id: params.Selection = params.Selection()

    material: params.Selection = params.Selection(value=MATERIAL_OPTIONS[0], options=MATERIAL_OPTIONS)


@dataclass(frozen=True)
class Params:
    json_file: params.File
    layer_range: params.IntRange
    layer_selection_active_flg: params.Bool
    detect_layers_flg: params.Bool
    start_point_rotation_flg: params.Bool
    alternate_direction_for_each_layer_flg: params.Bool
    layer_thickness: params.Float
    layer_step_over: params.Float
    offset_to_curve: params.Float
    min_angle_definition: params.Float
    max_angle_definition: params.Float
    basic_tilt_angle: params.Float
    pattern: params.Selection
    posting_definition_id: params.Selection
    material: params.Selection

    @staticmethod
    def create(storage: ParameterStorage) -> Params:
        return Params(
            storage.json_file,
            storage.layer_range,
            storage.layer_selection_active_flg,
            storage.detect_layers_flg,
            storage.start_point_rotation_flg,
            storage.alternate_direction_for_each_layer_flg,
            storage.layer_thickness,
            storage.layer_step_over,
            storage.offset_to_curve,
            storage.min_angle_definition,
            storage.max_angle_definition,
            storage.basic_tilt_angle,
            storage.pattern,
            storage.posting_definition_id,
            storage.material,
        )


def get_ui(b: ui.Builder, params: Params) -> list[ui.UiNode]:
    params.detect_layers_flg.map(params.layer_selection_active_flg, lambda v: not v)

    # Use the shared state material selection for synchronization
    material_selection = state["material"]
    mat = material_selection.value

    def on_material_change(selected_material):
        props = MATERIAL_TO_PROPS.get(selected_material)
        if props:
            params.layer_thickness.value = props["layer_thickness"]
            params.layer_step_over.value = props["layer_stepover"]

            # Update the current parameter objects for the Welding Parameters tab
            state["current_welding_current"].value = props["welding_parameters"]["current_set"]
            state["current_welding_voltage"].value = props["welding_parameters"]["voltage_set"]
            state["current_welding_speed"].value = props["welding_parameters"]["travel_speed"]

            # Only update the values of the existing parameter objects, never replace them
            if selected_material not in state["welding_params"]:
                from tcframework import params as tcparams
                state["welding_params"][selected_material] = {
                    "welding_current": tcparams.Int(value=props["welding_parameters"]["current_set"]),
                    "welding_voltage": tcparams.Int(value=props["welding_parameters"]["voltage_set"]),
                    "welding_speed": tcparams.Int(value=props["welding_parameters"]["travel_speed"]),
                }
            else:
                state["welding_params"][selected_material]["welding_current"].value = props["welding_parameters"]["current_set"]
                state["welding_params"][selected_material]["welding_voltage"].value = props["welding_parameters"]["voltage_set"]
                state["welding_params"][selected_material]["welding_speed"].value = props["welding_parameters"]["travel_speed"]
            # Increment version to force re-render in the Welding Parameters tab
            state["version"].value += 1

    # Always connect the on_change handler (even if already set)
    material_selection.on_change(lambda v: on_material_change(v))

    return [
        box_ui.drop_down(b, material_selection, text="Material"),
        ui.active_if(
            b,
            params.layer_selection_active_flg,
            box_ui.number_range(b, params.layer_range, text="Layer Range"),
        ),
        box_ui.checkbox(b, params.detect_layers_flg, text="Detect Layers"),
        box_ui.number_field(b, params.layer_thickness, text="Layer Thickness"),
        box_ui.number_field(b, params.layer_step_over, text="Layer Step Over"),
        box_ui.number_field(b, params.offset_to_curve, text="Offset to Curve"),
        box_ui.number_field(b, params.basic_tilt_angle, text="Basic Tilt Angle"),
    ]
